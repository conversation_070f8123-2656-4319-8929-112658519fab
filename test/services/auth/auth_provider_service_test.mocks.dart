// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in noeji/test/services/auth/auth_provider_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:google_sign_in/google_sign_in.dart' as _i2;
import 'package:google_sign_in_platform_interface/google_sign_in_platform_interface.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGoogleSignInAuthentication_0 extends _i1.SmartFake
    implements _i2.GoogleSignInAuthentication {
  _FakeGoogleSignInAuthentication_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GoogleSignIn].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleSignIn extends _i1.Mock implements _i2.GoogleSignIn {
  MockGoogleSignIn() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.SignInOption get signInOption =>
      (super.noSuchMethod(
            Invocation.getter(#signInOption),
            returnValue: _i3.SignInOption.standard,
          )
          as _i3.SignInOption);

  @override
  List<String> get scopes =>
      (super.noSuchMethod(Invocation.getter(#scopes), returnValue: <String>[])
          as List<String>);

  @override
  bool get forceCodeForRefreshToken =>
      (super.noSuchMethod(
            Invocation.getter(#forceCodeForRefreshToken),
            returnValue: false,
          )
          as bool);

  @override
  _i4.Stream<_i2.GoogleSignInAccount?> get onCurrentUserChanged =>
      (super.noSuchMethod(
            Invocation.getter(#onCurrentUserChanged),
            returnValue: _i4.Stream<_i2.GoogleSignInAccount?>.empty(),
          )
          as _i4.Stream<_i2.GoogleSignInAccount?>);

  @override
  _i4.Future<_i2.GoogleSignInAccount?> signInSilently({
    bool? suppressErrors = true,
    bool? reAuthenticate = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInSilently, [], {
              #suppressErrors: suppressErrors,
              #reAuthenticate: reAuthenticate,
            }),
            returnValue: _i4.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i4.Future<_i2.GoogleSignInAccount?>);

  @override
  _i4.Future<bool> isSignedIn() =>
      (super.noSuchMethod(
            Invocation.method(#isSignedIn, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i2.GoogleSignInAccount?> signIn() =>
      (super.noSuchMethod(
            Invocation.method(#signIn, []),
            returnValue: _i4.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i4.Future<_i2.GoogleSignInAccount?>);

  @override
  _i4.Future<_i2.GoogleSignInAccount?> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i4.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i4.Future<_i2.GoogleSignInAccount?>);

  @override
  _i4.Future<_i2.GoogleSignInAccount?> disconnect() =>
      (super.noSuchMethod(
            Invocation.method(#disconnect, []),
            returnValue: _i4.Future<_i2.GoogleSignInAccount?>.value(),
          )
          as _i4.Future<_i2.GoogleSignInAccount?>);

  @override
  _i4.Future<bool> requestScopes(List<String>? scopes) =>
      (super.noSuchMethod(
            Invocation.method(#requestScopes, [scopes]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> canAccessScopes(
    List<String>? scopes, {
    String? accessToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #canAccessScopes,
              [scopes],
              {#accessToken: accessToken},
            ),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);
}

/// A class which mocks [GoogleSignInAccount].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockGoogleSignInAccount extends _i1.Mock
    implements _i2.GoogleSignInAccount {
  MockGoogleSignInAccount() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get email =>
      (super.noSuchMethod(
            Invocation.getter(#email),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#email),
            ),
          )
          as String);

  @override
  String get id =>
      (super.noSuchMethod(
            Invocation.getter(#id),
            returnValue: _i5.dummyValue<String>(this, Invocation.getter(#id)),
          )
          as String);

  @override
  _i4.Future<_i2.GoogleSignInAuthentication> get authentication =>
      (super.noSuchMethod(
            Invocation.getter(#authentication),
            returnValue: _i4.Future<_i2.GoogleSignInAuthentication>.value(
              _FakeGoogleSignInAuthentication_0(
                this,
                Invocation.getter(#authentication),
              ),
            ),
          )
          as _i4.Future<_i2.GoogleSignInAuthentication>);

  @override
  _i4.Future<Map<String, String>> get authHeaders =>
      (super.noSuchMethod(
            Invocation.getter(#authHeaders),
            returnValue: _i4.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i4.Future<Map<String, String>>);

  @override
  _i4.Future<void> clearAuthCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearAuthCache, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [GoogleSignInAuthentication].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleSignInAuthentication extends _i1.Mock
    implements _i2.GoogleSignInAuthentication {
  MockGoogleSignInAuthentication() {
    _i1.throwOnMissingStub(this);
  }
}
