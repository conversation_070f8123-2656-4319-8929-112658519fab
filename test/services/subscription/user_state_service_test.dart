import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/subscription/user_state_service.dart';

// Mock classes
class MockCustomerInfo extends Mock implements CustomerInfo {}
class MockEntitlements extends Mock implements EntitlementInfos {}
class MockEntitlementInfo extends Mock implements EntitlementInfo {}

void main() {
  group('UserStateService', () {
    late UserStateService userStateService;
    late MockCustomerInfo mockCustomerInfo;
    late MockEntitlements mockEntitlements;
    late MockEntitlementInfo mockEntitlementInfo;

    setUp(() {
      userStateService = UserStateService.instance;
      mockCustomerInfo = MockCustomerInfo();
      mockEntitlements = MockEntitlements();
      mockEntitlementInfo = MockEntitlementInfo();
    });

    group('getUserState', () {
      test('returns trialUser when user has active trial entitlement', () {
        // Arrange
        when(mockCustomerInfo.entitlements).thenReturn(mockEntitlements);
        when(mockEntitlements.active).thenReturn({'pro': mockEntitlementInfo});
        when(mockEntitlementInfo.periodType).thenReturn(PeriodType.trial);

        // Act
        final result = userStateService.getUserState(mockCustomerInfo);

        // Assert
        expect(result, UserState.trialUser);
      });

      test('returns proUser when user has active paid entitlement', () {
        // Arrange
        when(mockCustomerInfo.entitlements).thenReturn(mockEntitlements);
        when(mockEntitlements.active).thenReturn({'pro': mockEntitlementInfo});
        when(mockEntitlementInfo.periodType).thenReturn(PeriodType.normal);

        // Act
        final result = userStateService.getUserState(mockCustomerInfo);

        // Assert
        expect(result, UserState.proUser);
      });

      test('returns freeUser when user has no active entitlement but has purchase history', () {
        // Arrange
        when(mockCustomerInfo.entitlements).thenReturn(mockEntitlements);
        when(mockEntitlements.active).thenReturn(<String, EntitlementInfo>{});
        when(mockCustomerInfo.allPurchaseDates).thenReturn({'some_product': '2024-01-01T00:00:00Z'});

        // Act
        final result = userStateService.getUserState(mockCustomerInfo);

        // Assert
        expect(result, UserState.freeUser);
      });

      test('returns newUser when user has no active entitlement and no purchase history', () {
        // Arrange
        when(mockCustomerInfo.entitlements).thenReturn(mockEntitlements);
        when(mockEntitlements.active).thenReturn(<String, EntitlementInfo>{});
        when(mockCustomerInfo.allPurchaseDates).thenReturn(<String, String?>{});

        // Act
        final result = userStateService.getUserState(mockCustomerInfo);

        // Assert
        expect(result, UserState.newUser);
      });

      test('returns unknown when an exception occurs', () {
        // Arrange
        when(mockCustomerInfo.entitlements).thenThrow(Exception('Test exception'));

        // Act
        final result = userStateService.getUserState(mockCustomerInfo);

        // Assert
        expect(result, UserState.unknown);
      });
    });

    group('helper methods', () {
      test('hasFullAccess returns true for trialUser and proUser', () {
        expect(userStateService.hasFullAccess(UserState.trialUser), true);
        expect(userStateService.hasFullAccess(UserState.proUser), true);
        expect(userStateService.hasFullAccess(UserState.freeUser), false);
        expect(userStateService.hasFullAccess(UserState.newUser), false);
        expect(userStateService.hasFullAccess(UserState.unknown), false);
      });

      test('shouldShowPaywall returns true only for newUser', () {
        expect(userStateService.shouldShowPaywall(UserState.newUser), true);
        expect(userStateService.shouldShowPaywall(UserState.trialUser), false);
        expect(userStateService.shouldShowPaywall(UserState.proUser), false);
        expect(userStateService.shouldShowPaywall(UserState.freeUser), false);
        expect(userStateService.shouldShowPaywall(UserState.unknown), false);
      });

      test('hasReadOnlyAccess returns true only for freeUser', () {
        expect(userStateService.hasReadOnlyAccess(UserState.freeUser), true);
        expect(userStateService.hasReadOnlyAccess(UserState.newUser), false);
        expect(userStateService.hasReadOnlyAccess(UserState.trialUser), false);
        expect(userStateService.hasReadOnlyAccess(UserState.proUser), false);
        expect(userStateService.hasReadOnlyAccess(UserState.unknown), false);
      });

      test('isProUser returns true for users with full access', () {
        expect(userStateService.isProUser(UserState.trialUser), true);
        expect(userStateService.isProUser(UserState.proUser), true);
        expect(userStateService.isProUser(UserState.freeUser), false);
        expect(userStateService.isProUser(UserState.newUser), false);
        expect(userStateService.isProUser(UserState.unknown), false);
      });

      test('getUserTierString returns correct tier strings', () {
        expect(userStateService.getUserTierString(UserState.trialUser), 'pro');
        expect(userStateService.getUserTierString(UserState.proUser), 'pro');
        expect(userStateService.getUserTierString(UserState.freeUser), 'free');
        expect(userStateService.getUserTierString(UserState.newUser), 'free');
        expect(userStateService.getUserTierString(UserState.unknown), 'free');
      });
    });
  });
}
