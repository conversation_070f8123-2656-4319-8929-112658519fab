// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in noeji/test/ui/providers/chat_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:noeji/models/chat.dart' as _i2;
import 'package:noeji/models/chat_message.dart' as _i7;
import 'package:noeji/models/models.dart' as _i4;
import 'package:noeji/repositories/chat_file_repository.dart' as _i5;
import 'package:noeji/repositories/idea_repository.dart' as _i10;
import 'package:noeji/services/firebase/firestore_service.dart' as _i11;
import 'package:noeji/services/llm/gemini_chat_service.dart' as _i3;
import 'package:noeji/services/remote_config/remote_config_providers.dart'
    as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeChat_0 extends _i1.SmartFake implements _i2.Chat {
  _FakeChat_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeChatResponse_1 extends _i1.SmartFake implements _i3.ChatResponse {
  _FakeChatResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdea_2 extends _i1.SmartFake implements _i4.Idea {
  _FakeIdea_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdeabook_3 extends _i1.SmartFake implements _i4.Ideabook {
  _FakeIdeabook_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNote_4 extends _i1.SmartFake implements _i4.Note {
  _FakeNote_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ChatFileRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockChatFileRepository extends _i1.Mock
    implements _i5.ChatFileRepository {
  MockChatFileRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.Chat?> getChatByIdeabookId(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getChatByIdeabookId, [ideabookId]),
            returnValue: _i6.Future<_i2.Chat?>.value(),
          )
          as _i6.Future<_i2.Chat?>);

  @override
  _i6.Future<_i2.Chat> createChat({
    required String? ideabookId,
    List<_i7.ChatMessage>? messages = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createChat, [], {
              #ideabookId: ideabookId,
              #messages: messages,
            }),
            returnValue: _i6.Future<_i2.Chat>.value(
              _FakeChat_0(
                this,
                Invocation.method(#createChat, [], {
                  #ideabookId: ideabookId,
                  #messages: messages,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.Chat>);

  @override
  _i6.Future<bool> updateChat(_i2.Chat? chat) =>
      (super.noSuchMethod(
            Invocation.method(#updateChat, [chat]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> addMessageToChat({
    required _i2.Chat? chat,
    required _i7.MessageRole? role,
    required String? content,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#addMessageToChat, [], {
              #chat: chat,
              #role: role,
              #content: content,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteChat(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteChat, [ideabookId]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> updateMessage({
    required _i2.Chat? chat,
    required String? messageId,
    required _i7.ChatMessage? updatedMessage,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateMessage, [], {
              #chat: chat,
              #messageId: messageId,
              #updatedMessage: updatedMessage,
            }),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);
}

/// A class which mocks [GeminiChatService].
///
/// See the documentation for Mockito's code generation for more information.
class MockGeminiChatService extends _i1.Mock implements _i3.GeminiChatService {
  MockGeminiChatService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<_i3.ChatResponse> chatWithSystemInstruction({
    required List<_i7.ChatMessage>? messages,
    required String? systemInstruction,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#chatWithSystemInstruction, [], {
              #messages: messages,
              #systemInstruction: systemInstruction,
            }),
            returnValue: _i6.Future<_i3.ChatResponse>.value(
              _FakeChatResponse_1(
                this,
                Invocation.method(#chatWithSystemInstruction, [], {
                  #messages: messages,
                  #systemInstruction: systemInstruction,
                }),
              ),
            ),
          )
          as _i6.Future<_i3.ChatResponse>);

  @override
  _i6.Future<_i3.ChatResponse> chat(List<_i7.ChatMessage>? messages) =>
      (super.noSuchMethod(
            Invocation.method(#chat, [messages]),
            returnValue: _i6.Future<_i3.ChatResponse>.value(
              _FakeChatResponse_1(this, Invocation.method(#chat, [messages])),
            ),
          )
          as _i6.Future<_i3.ChatResponse>);

  @override
  _i6.Future<_i3.ChatResponse> generateSuggestedPrompts(
    String? promptContent,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#generateSuggestedPrompts, [promptContent]),
            returnValue: _i6.Future<_i3.ChatResponse>.value(
              _FakeChatResponse_1(
                this,
                Invocation.method(#generateSuggestedPrompts, [promptContent]),
              ),
            ),
          )
          as _i6.Future<_i3.ChatResponse>);
}

/// A class which mocks [LlmPrompts].
///
/// See the documentation for Mockito's code generation for more information.
class MockLlmPrompts extends _i1.Mock implements _i8.LlmPrompts {
  MockLlmPrompts() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String getNewIdeabookPrompt({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getNewIdeabookPrompt, [], {#userTier: userTier}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getNewIdeabookPrompt, [], {
                #userTier: userTier,
              }),
            ),
          )
          as String);

  @override
  String getNewIdeaPrompt({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getNewIdeaPrompt, [], {#userTier: userTier}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getNewIdeaPrompt, [], {#userTier: userTier}),
            ),
          )
          as String);

  @override
  String getChatInputPrompt({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getChatInputPrompt, [], {#userTier: userTier}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getChatInputPrompt, [], {#userTier: userTier}),
            ),
          )
          as String);

  @override
  String getChatPrompt({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getChatPrompt, [], {#userTier: userTier}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getChatPrompt, [], {#userTier: userTier}),
            ),
          )
          as String);

  @override
  String getSuggestedPromptsPrompt({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getSuggestedPromptsPrompt, [], {
              #userTier: userTier,
            }),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getSuggestedPromptsPrompt, [], {
                #userTier: userTier,
              }),
            ),
          )
          as String);

  @override
  String getNoteRegenerationPrompt({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getNoteRegenerationPrompt, [], {
              #userTier: userTier,
            }),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getNoteRegenerationPrompt, [], {
                #userTier: userTier,
              }),
            ),
          )
          as String);

  @override
  bool isGenerativeSuggestedPromptsEnabled({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#isGenerativeSuggestedPromptsEnabled, [], {
              #userTier: userTier,
            }),
            returnValue: false,
          )
          as bool);

  @override
  String getChatSystemInstruction({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getChatSystemInstruction, [], {
              #userTier: userTier,
            }),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getChatSystemInstruction, [], {
                #userTier: userTier,
              }),
            ),
          )
          as String);

  @override
  String getChatUserInstruction({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getChatUserInstruction, [], {
              #userTier: userTier,
            }),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#getChatUserInstruction, [], {
                #userTier: userTier,
              }),
            ),
          )
          as String);

  @override
  Map<String, dynamic> getChatResponseStyle({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getChatResponseStyle, [], {#userTier: userTier}),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, String> getDefaultChatResponseStyle({String? userTier}) =>
      (super.noSuchMethod(
            Invocation.method(#getDefaultChatResponseStyle, [], {
              #userTier: userTier,
            }),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  String replaceVariables(String? prompt, Map<String, String>? variables) =>
      (super.noSuchMethod(
            Invocation.method(#replaceVariables, [prompt, variables]),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#replaceVariables, [prompt, variables]),
            ),
          )
          as String);
}

/// A class which mocks [IdeaRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockIdeaRepository extends _i1.Mock implements _i10.IdeaRepository {
  MockIdeaRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<List<_i4.Idea>> getIdeasByIdeabookId(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeasByIdeabookId, [ideabookId]),
            returnValue: _i6.Future<List<_i4.Idea>>.value(<_i4.Idea>[]),
          )
          as _i6.Future<List<_i4.Idea>>);

  @override
  _i6.Stream<List<_i4.Idea>> getIdeasByIdeabookIdStream(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeasByIdeabookIdStream, [ideabookId]),
            returnValue: _i6.Stream<List<_i4.Idea>>.empty(),
          )
          as _i6.Stream<List<_i4.Idea>>);

  @override
  _i6.Future<List<_i4.Idea>> getAll() =>
      (super.noSuchMethod(
            Invocation.method(#getAll, []),
            returnValue: _i6.Future<List<_i4.Idea>>.value(<_i4.Idea>[]),
          )
          as _i6.Future<List<_i4.Idea>>);

  @override
  _i6.Future<_i4.Idea?> getById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getById, [id]),
            returnValue: _i6.Future<_i4.Idea?>.value(),
          )
          as _i6.Future<_i4.Idea?>);

  @override
  _i6.Future<_i4.Idea?> getIdeaById(String? ideabookId, String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeaById, [ideabookId, id]),
            returnValue: _i6.Future<_i4.Idea?>.value(),
          )
          as _i6.Future<_i4.Idea?>);

  @override
  _i6.Stream<_i4.Idea?> getIdeaByIdStream(String? ideabookId, String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeaByIdStream, [ideabookId, id]),
            returnValue: _i6.Stream<_i4.Idea?>.empty(),
          )
          as _i6.Stream<_i4.Idea?>);

  @override
  _i6.Future<_i4.Idea> create(Map<String, dynamic>? data) =>
      (super.noSuchMethod(
            Invocation.method(#create, [data]),
            returnValue: _i6.Future<_i4.Idea>.value(
              _FakeIdea_2(this, Invocation.method(#create, [data])),
            ),
          )
          as _i6.Future<_i4.Idea>);

  @override
  _i6.Future<int> getIdeaCount(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeaCount, [ideabookId]),
            returnValue: _i6.Future<int>.value(0),
          )
          as _i6.Future<int>);

  @override
  _i6.Future<bool> isIdeabookFull(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#isIdeabookFull, [ideabookId]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<_i4.Idea> createIdea({
    required String? ideabookId,
    required String? content,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createIdea, [], {
              #ideabookId: ideabookId,
              #content: content,
            }),
            returnValue: _i6.Future<_i4.Idea>.value(
              _FakeIdea_2(
                this,
                Invocation.method(#createIdea, [], {
                  #ideabookId: ideabookId,
                  #content: content,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.Idea>);

  @override
  _i6.Future<bool> update(_i4.Idea? entity) =>
      (super.noSuchMethod(
            Invocation.method(#update, [entity]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> updateIdea(String? ideabookId, _i4.Idea? idea) =>
      (super.noSuchMethod(
            Invocation.method(#updateIdea, [ideabookId, idea]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteIdea(String? ideabookId, String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteIdea, [ideabookId, id]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  void logOperation(
    String? operation, {
    String? id,
    String? details,
    Object? error,
  }) => super.noSuchMethod(
    Invocation.method(
      #logOperation,
      [operation],
      {#id: id, #details: details, #error: error},
    ),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FirestoreService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirestoreService extends _i1.Mock implements _i11.FirestoreService {
  MockFirestoreService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<List<_i4.Ideabook>> listenToIdeabooks() =>
      (super.noSuchMethod(
            Invocation.method(#listenToIdeabooks, []),
            returnValue: _i6.Stream<List<_i4.Ideabook>>.empty(),
          )
          as _i6.Stream<List<_i4.Ideabook>>);

  @override
  _i6.Future<List<_i4.Ideabook>> getAllIdeabooks() =>
      (super.noSuchMethod(
            Invocation.method(#getAllIdeabooks, []),
            returnValue: _i6.Future<List<_i4.Ideabook>>.value(<_i4.Ideabook>[]),
          )
          as _i6.Future<List<_i4.Ideabook>>);

  @override
  _i6.Stream<_i4.Ideabook?> listenToIdeabookById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#listenToIdeabookById, [id]),
            returnValue: _i6.Stream<_i4.Ideabook?>.empty(),
          )
          as _i6.Stream<_i4.Ideabook?>);

  @override
  _i6.Future<_i4.Ideabook?> getIdeabookById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeabookById, [id]),
            returnValue: _i6.Future<_i4.Ideabook?>.value(),
          )
          as _i6.Future<_i4.Ideabook?>);

  @override
  _i6.Future<_i4.Ideabook> createIdeabook(_i4.Ideabook? ideabook) =>
      (super.noSuchMethod(
            Invocation.method(#createIdeabook, [ideabook]),
            returnValue: _i6.Future<_i4.Ideabook>.value(
              _FakeIdeabook_3(
                this,
                Invocation.method(#createIdeabook, [ideabook]),
              ),
            ),
          )
          as _i6.Future<_i4.Ideabook>);

  @override
  _i6.Future<bool> updateIdeabook(_i4.Ideabook? ideabook) =>
      (super.noSuchMethod(
            Invocation.method(#updateIdeabook, [ideabook]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteIdeabook(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteIdeabook, [id]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Stream<List<_i4.Idea>> listenToIdeas(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToIdeas, [ideabookId]),
            returnValue: _i6.Stream<List<_i4.Idea>>.empty(),
          )
          as _i6.Stream<List<_i4.Idea>>);

  @override
  _i6.Future<List<_i4.Idea>> getIdeasByIdeabookId(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeasByIdeabookId, [ideabookId]),
            returnValue: _i6.Future<List<_i4.Idea>>.value(<_i4.Idea>[]),
          )
          as _i6.Future<List<_i4.Idea>>);

  @override
  _i6.Stream<_i4.Idea?> listenToIdeaById(String? ideabookId, String? ideaId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToIdeaById, [ideabookId, ideaId]),
            returnValue: _i6.Stream<_i4.Idea?>.empty(),
          )
          as _i6.Stream<_i4.Idea?>);

  @override
  _i6.Future<_i4.Idea?> getIdeaById(String? ideabookId, String? ideaId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeaById, [ideabookId, ideaId]),
            returnValue: _i6.Future<_i4.Idea?>.value(),
          )
          as _i6.Future<_i4.Idea?>);

  @override
  _i6.Future<_i4.Idea> createIdea(String? ideabookId, _i4.Idea? idea) =>
      (super.noSuchMethod(
            Invocation.method(#createIdea, [ideabookId, idea]),
            returnValue: _i6.Future<_i4.Idea>.value(
              _FakeIdea_2(
                this,
                Invocation.method(#createIdea, [ideabookId, idea]),
              ),
            ),
          )
          as _i6.Future<_i4.Idea>);

  @override
  _i6.Future<bool> updateIdea(String? ideabookId, _i4.Idea? idea) =>
      (super.noSuchMethod(
            Invocation.method(#updateIdea, [ideabookId, idea]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> updateIdeaSortOrder(
    String? ideabookId,
    String? ideaId,
    double? newSortOrder,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateIdeaSortOrder, [
              ideabookId,
              ideaId,
              newSortOrder,
            ]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteIdea(String? ideabookId, String? ideaId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteIdea, [ideabookId, ideaId]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Stream<List<_i4.Note>> listenToNotes(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToNotes, [ideabookId]),
            returnValue: _i6.Stream<List<_i4.Note>>.empty(),
          )
          as _i6.Stream<List<_i4.Note>>);

  @override
  _i6.Future<List<_i4.Note>> getNotesByIdeabookId(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotesByIdeabookId, [ideabookId]),
            returnValue: _i6.Future<List<_i4.Note>>.value(<_i4.Note>[]),
          )
          as _i6.Future<List<_i4.Note>>);

  @override
  _i6.Stream<_i4.Note?> listenToNoteById(String? ideabookId, String? noteId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToNoteById, [ideabookId, noteId]),
            returnValue: _i6.Stream<_i4.Note?>.empty(),
          )
          as _i6.Stream<_i4.Note?>);

  @override
  _i6.Future<_i4.Note?> getNoteById(String? ideabookId, String? noteId) =>
      (super.noSuchMethod(
            Invocation.method(#getNoteById, [ideabookId, noteId]),
            returnValue: _i6.Future<_i4.Note?>.value(),
          )
          as _i6.Future<_i4.Note?>);

  @override
  _i6.Future<_i4.Note> createNote(String? ideabookId, _i4.Note? note) =>
      (super.noSuchMethod(
            Invocation.method(#createNote, [ideabookId, note]),
            returnValue: _i6.Future<_i4.Note>.value(
              _FakeNote_4(
                this,
                Invocation.method(#createNote, [ideabookId, note]),
              ),
            ),
          )
          as _i6.Future<_i4.Note>);

  @override
  _i6.Future<bool> updateNote(String? ideabookId, _i4.Note? note) =>
      (super.noSuchMethod(
            Invocation.method(#updateNote, [ideabookId, note]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> updateNoteSortOrder(
    String? ideabookId,
    String? noteId,
    double? newSortOrder,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateNoteSortOrder, [
              ideabookId,
              noteId,
              newSortOrder,
            ]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteNote(String? ideabookId, String? noteId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteNote, [ideabookId, noteId]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> savePasscodeHash(String? passcodeHash) =>
      (super.noSuchMethod(
            Invocation.method(#savePasscodeHash, [passcodeHash]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Stream<String?> listenToPasscodeHash() =>
      (super.noSuchMethod(
            Invocation.method(#listenToPasscodeHash, []),
            returnValue: _i6.Stream<String?>.empty(),
          )
          as _i6.Stream<String?>);

  @override
  _i6.Future<bool> saveCustomSystemPrompt(String? systemPrompt) =>
      (super.noSuchMethod(
            Invocation.method(#saveCustomSystemPrompt, [systemPrompt]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Stream<String?> listenToCustomSystemPrompt() =>
      (super.noSuchMethod(
            Invocation.method(#listenToCustomSystemPrompt, []),
            returnValue: _i6.Stream<String?>.empty(),
          )
          as _i6.Stream<String?>);

  @override
  _i6.Future<String?> getPasscodeHash() =>
      (super.noSuchMethod(
            Invocation.method(#getPasscodeHash, []),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<bool> isPasscodeSet() =>
      (super.noSuchMethod(
            Invocation.method(#isPasscodeSet, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> validatePasscode(String? passcode) =>
      (super.noSuchMethod(
            Invocation.method(#validatePasscode, [passcode]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);
}
