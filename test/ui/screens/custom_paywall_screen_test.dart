import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/screens/custom_paywall_screen.dart';
import 'package:noeji/ui/theme/theme_provider.dart';

void main() {
  group('CustomPaywallScreen', () {
    testWidgets('should display loading screen initially', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: ThemeData.light(),
            home: const CustomPaywallScreen(),
          ),
        ),
      );

      // Verify loading screen is displayed
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading subscription options...'), findsOneWidget);
    });

    testWidgets('should be non-dismissible', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: ThemeData.light(),
            home: const CustomPaywallScreen(),
          ),
        ),
      );

      // Verify PopScope is present and canPop is false
      final popScope = tester.widget<PopScope>(find.byType(PopScope));
      expect(popScope.canPop, false);
    });

    testWidgets('should display Noeji Pro logo', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: ThemeData.light(),
            home: const CustomPaywallScreen(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pump();

      // The logo should be present (though offerings might not load in test)
      // We can't easily test the logo without mocking RevenueCat
    });
  });
}
