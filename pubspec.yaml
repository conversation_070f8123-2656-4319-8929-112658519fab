name: noeji
description: "A mobile app that helps quickly capture daily fragmented ideas."
publish_to: 'none'
version: 1.1.7+117

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  # Flutter icons
  cupertino_icons: ^1.0.6
  # State management
  flutter_riverpod: ^2.4.9

  # File system
  path_provider: ^2.1.1
  path: ^1.8.3

  # Utilities
  intl: ^0.19.0
  shared_preferences: ^2.2.2
  crypto: ^3.0.3

  # Audio
  record: ^5.0.4
  permission_handler: ^11.3.0
  waveform_flutter: ^1.2.0

  # Fonts
  google_fonts: ^6.1.0

  # Networking
  http: ^1.3.0

  # Markdown
  flutter_markdown: ^0.6.18

  # Firebase
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.3
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1

  # SVG
  flutter_svg: ^2.0.9
  in_app_purchase: ^3.2.1
  cloud_firestore: ^5.6.7

  # URL launcher for paywall links
  url_launcher: ^6.2.5

  # WebView
  webview_flutter: ^4.7.0

  # Onboarding
  showcaseview: ^4.0.1
  firebase_app_check: ^0.3.2+6
  purchases_flutter: ^8.8.1
  purchases_ui_flutter: ^8.8.1
  firebase_remote_config: ^5.4.4
  firebase_analytics: ^11.4.6
  firebase_ai: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.4

  # Code generation
  build_runner: ^2.4.7
  in_app_purchase_platform_interface: ^1.4.0

  # App icon generation
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/social/
    - assets/fonts/
    - noeji_tos/noeji_app_tos/terms_of_service.html

  fonts:
    - family: Pacifico
      fonts:
        - asset: assets/fonts/Pacifico-Regular.ttf
    - family: Afacad
      fonts:
        - asset: assets/fonts/Afacad-Regular.ttf
        - asset: assets/fonts/Afacad-Medium.ttf
          weight: 500
        - asset: assets/fonts/Afacad-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Afacad-Bold.ttf
          weight: 700
    - family: AllertaStencil
      fonts:
        - asset: assets/fonts/AllertaStencil-Regular.ttf
    - family: Acme
      fonts:
        - asset: assets/fonts/Acme-Regular.ttf

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: true
  ios: false  # iOS icons are already set up
  image_path: "AppIcon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "AppIcon.png"
  min_sdk_android: 21
