<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noeji Support - Ideas Spoken. Stories Born.</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Afacad', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff595e, #1982c4);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .logo {
            font-family: 'Pacifico', cursive;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .tagline {
            font-family: '<PERSON>ika', sans-serif;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        h2 {
            color: #1982c4;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #ff595e;
            padding-bottom: 10px;
        }

        h3 {
            color: #ff595e;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
        }

        p, li {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        ul {
            margin-left: 20px;
            margin-bottom: 20px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ff595e;
        }

        .color-palette {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .color-box {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            border: 2px solid #333;
        }

        .red { background-color: #ff595e; }
        .orange { background-color: #ff924c; }
        .yellow { background-color: #ffca3a; }
        .green { background-color: #8ac926; }
        .blue { background-color: #1982c4; }
        .purple { background-color: #6a4c93; }

        .contact {
            background: #f8f9fa;
            padding: 30px;
            margin-top: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .contact a {
            color: #1982c4;
            text-decoration: none;
            font-weight: bold;
        }

        .contact a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 5px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2em;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&family=Signika:wght@400;600&family=Afacad:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="logo">Noeji</h1>
            <p class="tagline">Ideas Spoken. Stories Born.</p>
        </div>

        <div class="content">
            <h2>Welcome to Noeji Support</h2>
            <p>Noeji is your personal AI-powered ideabook that helps you quickly capture daily fragmented ideas through voice memos and transform them into complete stories using generative AI.</p>

            <h2>Key Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎤 Voice Recording</h3>
                    <p>Capture ideas instantly with voice recordings. Our AI transcribes and refines your thoughts automatically.</p>
                </div>
                
                <div class="feature-card">
                    <h3>📚 Ideabooks</h3>
                    <p>Organize your ideas into themed collections. Each ideabook can be customized with colors and locked for privacy.</p>
                </div>
                
                <div class="feature-card">
                    <h3>🤖 AI Chat</h3>
                    <p>Chat with AI about your ideabook contents. Get insights, expand on ideas, and create complete stories.</p>
                </div>
                
                <div class="feature-card">
                    <h3>📝 Smart Notes</h3>
                    <p>Save AI responses as notes for future reference. Edit titles and regenerate content as needed.</p>
                </div>
            </div>

            <h2>How It Works</h2>
            
            <h3>Creating Ideabooks</h3>
            <ul>
                <li>Tap "New Ideabook" and describe what it's about</li>
                <li>AI will transcribe and create a short, descriptive name</li>
                <li>Choose from 6 beautiful colors to categorize your ideabook</li>
            </ul>

            <h3>Color System</h3>
            <p>Organize your ideabooks with our flexible color system:</p>
            <div class="color-palette">
                <div class="color-box red" title="Red - Work, Professional"></div>
                <div class="color-box orange" title="Orange - Travel, Planning, Events"></div>
                <div class="color-box yellow" title="Yellow - Inspiration, Diary, Journal"></div>
                <div class="color-box green" title="Green - Family, Friends, Leisure"></div>
                <div class="color-box blue" title="Blue - Personal Growth, Health"></div>
                <div class="color-box purple" title="Purple - Everything Else"></div>
            </div>

            <h3>Adding Ideas</h3>
            <ul>
                <li>Tap the microphone button on any ideabook</li>
                <li>Record your idea (up to 1 minute)</li>
                <li>AI transcribes, polishes, and adds emojis for delight</li>
                <li>Ideas are automatically organized by date</li>
            </ul>

            <h3>AI Chat & Notes</h3>
            <ul>
                <li>Chat with AI about your ideabook contents</li>
                <li>Get suggestions, expand ideas, or create stories</li>
                <li>Save valuable AI responses as notes</li>
                <li>Voice input supported in chat</li>
            </ul>

            <h2>Privacy & Security</h2>
            <ul>
                <li><strong>Passcode Protection:</strong> Lock sensitive ideabooks with a secure passcode</li>
                <li><strong>Cloud Sync:</strong> Your data is securely stored and synced across devices</li>
                <li><strong>Authentication:</strong> Sign in with Google for secure access</li>
                <li><strong>Local Storage:</strong> Chat history stored locally for privacy</li>
            </ul>

            <h2>Tips for Best Experience</h2>
            <ul>
                <li>Speak clearly when recording voice memos</li>
                <li>Use colors to categorize different types of ideas</li>
                <li>Regularly chat with AI to develop your ideas further</li>
                <li>Save important AI responses as notes for future reference</li>
                <li>Use drag-and-drop to reorder ideas within ideabooks</li>
            </ul>

            <h2>Subscription Plans</h2>
            
            <h3>Free Tier</h3>
            <ul>
                <li>Up to 3 ideabooks</li>
                <li>10 ideas per ideabook</li>
                <li>10 notes per ideabook</li>
                <li>10 chat messages per day</li>
                <li>10 transcriptions per day</li>
            </ul>

            <h3>Premium Tier</h3>
            <ul>
                <li>Up to 1,000 ideabooks</li>
                <li>100 ideas per ideabook</li>
                <li>100 notes per ideabook</li>
                <li>1,000 chat messages per day</li>
                <li>1,000 transcriptions per day</li>
            </ul>

            <div class="contact">
                <h2>Need Help?</h2>
                <p>If you have questions or need assistance, please contact our support team:</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>We're here to help you make the most of your Noeji experience!</p>
            </div>
        </div>
    </div>
</body>
</html>
