<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noeji Privacy Policy</title>
    <link href="https://fonts.googleapis.com/css2?family=Afacad:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Afacad', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333; /* Default text color */
        }
        h1 {
            font-family: 'Afacad', sans-serif;
            border-bottom: 1px solid #ccc; /* Lighter border */
            padding-bottom: 10px;
            font-weight: 700;
            color: #111; /* Darker heading color */
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            font-family: 'Afacad', sans-serif;
            margin-top: 30px;
            margin-bottom: 15px;
            font-weight: 700; /* Bolder for h2 */
            color: #222;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        h3 {
            font-family: 'Afacad', sans-serif;
            margin-top: 25px;
            margin-bottom: 10px;
            font-weight: 500;
            color: #333;
        }
        p, ul, ol {
            margin-bottom: 15px;
        }
        ul, ol {
            padding-left: 30px; /* Slightly more padding for lists */
        }
        li {
            margin-bottom: 8px; /* Spacing between list items */
        }
        strong {
            font-weight: 700;
        }
        em {
            font-style: italic;
        }
        .last-updated {
            text-align: center;
            font-style: italic;
            color: #555;
            margin-bottom: 30px;
        }
        .contact-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ccc;
        }
        .ai-clarification {
            background-color: #f9f9f9;
            border-left: 3px solid #007bff;
            padding: 10px 15px;
            margin: 15px 0;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <h1>Privacy Policy for Noeji</h1>
    <p class="last-updated"><em>Last Updated: May 31, 2025</em></p>

    <p>RocketByte LLC ("RocketByte," "we," "us," or "our") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our Noeji mobile application, web application (when available), and related services (collectively, the "Service").</p>

    <p>Please read this Privacy Policy carefully. <strong>BY ACCESSING OR USING THE SERVICE, YOU AGREE TO THE TERMS OF THIS PRIVACY POLICY. IF YOU DO NOT AGREE, DO NOT ACCESS OR USE THE SERVICE.</strong></p>

    <p>This Privacy Policy should be read in conjunction with our Terms of Service.</p>

    <h2>1. Information We Collect</h2>
    <p>In the course of providing the Service, we may collect various types of information from and about you, as described below. The specific types of information we collect may depend on the features you use and how you interact with the Service.</p>

    <h3>(A) Personal Data You Provide to Us:</h3>
    <ul>
        <li><strong>Account Information:</strong> When you register for an account, which may involve using third-party authentication providers (such as Google Sign-In or Apple Sign-In, or other providers we may support in the future), we receive certain profile information from these services or directly from you. This may include your name, email address, and profile picture, as permitted by your settings on those services, their privacy policies, or the information you provide during registration.</li>
        <li><strong>User Content:</strong> We collect the content and information you create, input, upload, or store within Noeji. This includes, but is not limited to, text, transcribed audio, information you organize into various structures within the app, prompts you provide to AI features, and any other materials you generate or manage through the Service ("User Content"). This User Content is processed by our systems, including AI/LLM components, to provide and enhance the features and functionality of the Service.</li>
        <li><strong>Audio Recordings for Transcription:</strong>To enable voice-to-text features, the Service captures your audio input. This audio data is immediately transmitted to our third-party AI service provider, Google (specifically, the Gemini API, which we use as a "Paid Service" under their terms), for the sole purpose of transcription. <strong>RocketByte itself does not store the raw audio recordings after they have been transmitted for transcription.</strong> As of the "Last Updated" date of this Privacy Policy, Google's terms for Paid Services of the Gemini API indicate that Google does not use your audio prompts or the transcribed responses from this Paid Service to improve their general AI models. Google states that they process this data in accordance with their Data Processing Addendum and may log prompts and responses for a limited time solely for abuse detection and legal/regulatory purposes. This data may be stored transiently or cached by Google as necessary to provide the transcription service. <strong>However, Google's terms and practices may change, and we encourage you to review their current relevant terms and data processing information for their AI services, including the [Gemini API Additional Terms of Service](https://ai.google.dev/gemini-api/terms), which are authoritative.</strong> The transcribed text, once returned to our Service, becomes part of your User Content.</li>
        <li><strong>Passcode Information:</strong> We store an encrypted salt (a non-reversible fingerprint) of the passcode you set to lock/unlock ideabooks. We do not store your actual passcode.</li>
        <li><strong>Communications:</strong> If you contact us directly (e.g., for support), we may receive additional information about you such as your name, email address, the contents of the message and/or attachments you may send us, and any other information you may choose to provide.</li>
    </ul>

    <h3>(B) Information We Collect Automatically:</h3>
    <ul>
        <li><strong>Usage Data:</strong> We automatically collect information about your interactions with the Service using Firebase Analytics and other tools. This includes features you use, actions you take (e.g., creating an ideabook, saving a note, chat message counts), ideabook color selections, sort order preferences, theme selection, filter settings, timestamps, and frequency of use. This may include aggregated data about the length of audio recordings or the number of words in ideabook names or ideas for the purpose of enforcing limits and improving the service.</li>
        <li><strong>Device and Technical Information:</strong> We collect information about the device you use to access the Service, such as your device model, operating system and version, app version, IP address (which may be used to infer general location), device identifiers, and crash data.</li>
        <li><strong>Location Information:</strong> With your permission (if requested by the app and if you enable location services for the App), we may collect your device's precise or approximate location to optimize results or provide location-based features. You can typically disable location services through your device settings.</li>
        <li><strong>Chat History (Local Storage):</strong> Your chat history with the LLM for each ideabook is stored locally on your device. It is not synced to the cloud and is lost if you switch devices or reinstall the app without a backup. While stored locally, if you engage in chat, the individual messages (your prompts and AI responses) are processed by our servers and third-party AI providers, who may have their own data retention policies for such processed data as outlined in their respective terms and privacy policies.</li>
    </ul>

    <h3>(C) Information from Third Parties:</h3>
    <ul>
        <li><strong>Authentication Providers:</strong> As mentioned, if you use a third-party service to create or log into your account (e.g., Google Sign-In, Apple Sign-In, or others we may offer), we receive profile information from that provider as permitted by their policies and your privacy settings on that service.</li>
        <li><strong>Subscription Management (RevenueCat):</strong> We receive your Firebase Auth UID and subscription status information from RevenueCat to manage your Free or Pro tier access. We do not receive or store your payment card details; these are handled by Apple App Store or Google Play Store.</li>
    </ul>

    <h2>2. How We Use Your Information</h2>
    <p>We use the information we collect for various purposes, including to:</p>
    <ul>
        <li>Provide, operate, maintain, and improve the Service.</li>
        <li>Create and manage your account, and authenticate your access.</li>
        <li>Process your User Content with AI/LLM services to provide the Service's functionalities.</li>
        <li>Personalize your experience (e.g., remember your preferences).</li>
        <li>Process subscriptions and manage user tiers (Free/Pro).</li>
        <li>Communicate with you, including responding to your inquiries, providing customer support, and sending you service-related announcements, updates, security alerts, and administrative messages.</li>
        <li>Monitor and analyze trends, usage, and activities in connection with our Service to improve user experience and performance. This may include using aggregated and anonymized data for our business purposes, such as data analysis, audits, fraud monitoring and prevention, diagnosing and fixing technical problems, developing new products and features, enhancing, improving or modifying our Service, identifying usage trends, determining the effectiveness of our promotional campaigns and operating and expanding our business activities.</li>
        <li>Enforce our Terms of Service, prevent abuse, and ensure the security and integrity of our Service (e.g., rate limiting, Firebase App Check).</li>
        <li>Comply with legal obligations and protect our rights and the rights of others.</li>
        <li>For any other purpose with your consent.</li>
    </ul>
    <div class="ai-clarification">
        <p><strong>AI Model Training Clarification:</strong> RocketByte LLC does not use your identifiable User Content to train its own proprietary AI models (if any exist separately from third-party provided models). When you use Noeji, your User Content (such as audio for transcription or text prompts for AI features) is processed by our third-party AI service provider, Google (using the Gemini API as a "Paid Service"). As of the "Last Updated" date of this Privacy Policy, Google's terms for such Paid Services (see [Gemini API Additional Terms of Service](https://ai.google.dev/gemini-api/terms)) state that Google does not use your prompts or responses from this Paid Service usage to improve their general AI models. Their processing is governed by their specific Data Processing Addendum. <strong>Because these third-party terms can be updated by Google, we encourage you to review them directly for the most current information on their data handling practices.</strong></p>
    </div>

    <h2>3. How We Share Your Information</h2>
    <p>We may share your information in the following situations:</p>
    <ul>
        <li><strong>With Third-Party Service Providers:</strong> We share information with third-party vendors, consultants, and other service providers who perform services on our behalf. These include:
            <ul>
                <li><strong>Cloud Infrastructure (Firebase by Google):</strong> For data storage (Firestore), authentication, serverless functions (Cloud Functions), remote configuration, analytics, and app security (App Check). Your User Content is stored on Firebase.</li>
                <li><strong>AI/LLM Provider (Google Gemini API - Paid Service):</strong> Your User Content (e.g., audio for transcription, text for AI features) is sent to Google's Gemini API (which we utilize as a "Paid Service" under their terms) for processing to enable Noeji's AI functionalities. As of the "Last Updated" date of this Privacy Policy, Google's terms for this Paid Service (see [Gemini API Additional Terms of Service](https://ai.google.dev/gemini-api/terms)) indicate they do not use your prompts or responses from this Paid Service to improve their general AI models and process this data under their Data Processing Addendum, logging data for a limited time for abuse detection and legal purposes. <strong>You acknowledge that Google's data handling practices are ultimately governed by their terms and data processing agreements, which can change, and which we encourage you to review.</strong></li>
                <li><strong>Subscription Management (RevenueCat):</strong> We share your Firebase Auth UID with RevenueCat to manage your subscription status.</li>
                <li><strong>Analytics Providers (Firebase Analytics):</strong> To help us understand usage patterns.</li>
            </ul>
        </li>
        <li><strong>For Legal Reasons:</strong> We may disclose your information if we believe it's necessary to:
            <ul>
                <li>Comply with applicable law, regulation, legal process, or governmental request.</li>
                <li>Enforce our Terms of Service, including investigation of potential violations.</li>
                <li>Detect, prevent, or otherwise address fraud, security, or technical issues.</li>
                <li>Protect the rights, property, or safety of RocketByte, our users, or the public as required or permitted by law.</li>
            </ul>
        </li>
        <li><strong>Business Transfers:</strong> In connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company, your information may be transferred as part of that transaction. We will use reasonable efforts to notify you via email and/or a prominent notice on our Service of any change in ownership or uses of your personal information, as well as any choices you may have.</li>
        <li><strong>Aggregated or Anonymized Data:</strong> We may share aggregated or anonymized information that does not directly identify you for research, analysis, or other purposes. For example, we may share statistics about app usage.</li>
        <li><strong>With Your Consent:</strong> We may share your information for other purposes with your explicit consent.</li>
    </ul>
    <p>We do not sell your personal information. Noeji does not currently display third-party advertisements. If this changes, we will update this Privacy Policy.</p>

    <h2>4. Data Storage and Security</h2>
    <ul>
        <li><strong>Data Location:</strong> Your data, including User Content stored in Firebase Firestore, is primarily stored and processed in the United States.</li>
        <li><strong>Security Measures:</strong> We implement reasonable administrative, technical, and physical security measures designed to protect your information from unauthorized access, use, alteration, or destruction. This includes using Firebase security features, encrypting passcode fingerprints (salts), and securing API keys.</li>
        <li><strong>Passcode Security:</strong> As stated in our Terms of Service, you are responsible for your passcode. It is not stored by us in a recoverable format.</li>
        <li><strong>Your Role in Security:</strong> In addition to the security measures we implement, the security of your information also depends on you. You are responsible for keeping your account credentials (including your social login details and any app-specific passcode) confidential and for using secure practices when accessing the Service.</li>
        <li><strong>Limitations:</strong> Despite our efforts, no security measures are perfect or impenetrable. We cannot guarantee the absolute security of your information. Any transmission of information is at your own risk.</li>
    </ul>

    <h2>5. Data Retention</h2>
    <p>We retain your personal information for as long as your account is active or as needed to provide you with the Service. We may also retain and use your information as necessary to comply with our legal obligations, resolve disputes, protect our assets, enforce our agreements, and for legitimate business purposes (e.g., improving the service with aggregated/anonymized data).</p>
    <ul>
        <li><strong>User Content:</strong> Your User Content stored in Firestore will be retained as long as your account is active, unless you delete them or your account is terminated.</li>
        <li><strong>Chat History:</strong> Chat history is stored locally on your device and is not retained by us on our servers beyond the immediate processing of individual messages by AI providers to generate responses.</li>
        <li><strong>Inactive Accounts:</strong> As per our Terms of Service, accounts inactive for twelve (12) months may be deleted, along with associated data, after a 30-day notice period.</li>
    </ul>

    <h2>6. Your Rights and Choices</h2>
    <p>Depending on your location and applicable law, you may have certain rights regarding your personal information. For US users, these may include:</p>
    <ul>
        <li><strong>Access and Update:</strong> You can access and update information that you create and store directly within Noeji, such as your ideabook names. The name and email address associated with your Google or Apple login are provided by those services. While you cannot change this information directly through Noeji, updates made in your Google or Apple account will generally be reflected in your Noeji profile upon subsequent logins. You can manage app connections and data sharing through your respective Google or Apple account settings.</li>
        <li><strong>Delete User Content:</strong> You can delete your ideabooks, ideas, and notes within the app. Deleting an ideabook will also delete its associated ideas and notes.</li>
        <li><strong>Clear Chat History:</strong> You can clear chat history for an ideabook within the app. This deletes it from your local device.</li>
        <li><strong>Account Deletion:</strong> You can request deletion of your account and associated personal data by contacting <NAME_EMAIL>. We will process such requests in accordance with applicable law and our data retention policies. Please note that some information may be retained for legal or legitimate business purposes, including for backup, archival, fraud prevention, or audit purposes, or as otherwise required by law. The methods for exercising these rights may be subject to limitations and exceptions under applicable law, and any interpretation of such rights will be made by RocketByte in its reasonable discretion consistent with applicable law.</li>
        <li><strong>Location Services:</strong> You can disable location services through your device settings if you do not want us to collect location information.</li>
        <li><strong>Promotional Communications:</strong> If we send promotional emails, you can opt-out by following the unsubscribe instructions in those emails. You may still receive transactional or administrative emails related to your account and the Service.</li>
    </ul>

    <h2>7. Children's Privacy</h2>
    <p>The Service is intended for users who are at least 18 years of age or older. We do not knowingly collect personal information from individuals under the age of 18. If we become aware that an individual under the age of 18 has provided us with personal information without verification of parental consent (where applicable for minors who may somehow gain access despite our terms, though our service is directed to those 18+), or in violation of our Terms of Service, we will take steps to delete such information from our files. If you are a parent or guardian and believe that an individual under the age of 18 (for whom you are responsible) has provided us with personal information, please contact <NAME_EMAIL> so that we can take appropriate action. By using the Service, you represent that you are at least 18 years old.</p>

    <h2>8. Third-Party Websites and Services</h2>
    <p>The Service may contain links to third-party websites or services that are not owned or controlled by RocketByte. This Privacy Policy does not apply to the practices of third parties that we do not own or control. We encourage you to review the privacy policies of any third-party service you interact with.</p>

    <h2>9. Changes to This Privacy Policy</h2>
    <p>We may update this Privacy Policy from time to time. If we make changes we determine to be material, we will endeavor to notify you before such changes become effective. Notification methods may include updating the "Last Updated" date at the top of this policy, providing notice through the Service (such as a pop-up or announcement), or, where we deem it appropriate for significant changes or if required by applicable law, by sending a notification to your registered email address. The timing and method of notice will be at our reasonable discretion, taking into account the nature of the changes and applicable legal requirements. We encourage you to review this Privacy Policy periodically to stay informed about our information practices. Your continued use of the Service after any changes to this Privacy Policy become effective constitutes your acceptance of such changes.</p>
    <div class="contact-info">
        <h2>10. Contact Us</h2>
        <p>If you have any questions or concerns about this Privacy Policy or our data practices, please contact us at:</p>
        <p>
            RocketByte LLC<br>
            Email: <EMAIL>
        </p>
    </div>
</body>
</html>
