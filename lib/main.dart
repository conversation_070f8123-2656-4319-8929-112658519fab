import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/chat/chat_rate_limit_service_provider.dart';
import 'package:noeji/services/chat/rate_limit_migration.dart';
import 'package:noeji/services/firebase/firebase_init.dart';
import 'package:noeji/services/firebase/firestore_listener_pool.dart';
import 'package:noeji/services/security/passcode_storage.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/services/subscription/app_lifecycle_service.dart';
import 'package:noeji/ui/providers/tour_completion_provider.dart';

import 'package:noeji/ui/screens/welcome_screen.dart';
import 'package:noeji/ui/theme/theme_provider.dart';
import 'package:noeji/ui/widgets/permission_error_overlay.dart';
import 'package:noeji/ui/widgets/showcase_tour.dart';
import 'package:noeji/ui/widgets/user_state_router.dart';
import 'package:noeji/utils/logger.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with emulator support
  try {
    await initializeFirebase();
    Logger.debug('Firebase initialized with emulator support');

    // Proactively refresh App Check token on app startup
    // This helps prevent stale token issues, especially after app restarts
    try {
      await refreshAppCheckToken();
      Logger.debug('App Check token refreshed on app startup');
    } catch (tokenError) {
      Logger.debug('Could not refresh App Check token on startup: $tokenError');
      // Don't fail app startup if token refresh fails
    }

    // Log the current auth state
    final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      Logger.debug('User is already signed in: ${currentUser.email}');
    } else {
      Logger.debug('No user is currently signed in');
    }

    // Set up auth state listener for debugging
    firebase_auth.FirebaseAuth.instance.idTokenChanges().listen((
      firebase_auth.User? user,
    ) {
      if (user == null) {
        Logger.debug('Auth state changed: User is signed out');
      } else {
        Logger.debug('Auth state changed: User is signed in (${user.email})');
      }
    });
  } catch (e) {
    Logger.error('Failed to initialize Firebase', e);
  }

  // Initialize shared preferences
  await SharedPreferences.getInstance();

  // Create a ProviderContainer to initialize PasscodeStorage
  final container = ProviderContainer();

  // Initialize PasscodeStorage with the container
  PasscodeStorage.initializeContainer(container);

  // Migrate from old rate limit system to new one
  await RateLimitMigration.migrateToNewSystem();

  // Initialize the chat rate limit service
  // This ensures the rate limit cache is loaded at app startup
  container.read(chatRateLimitServiceProvider);

  // Run the app with ProviderScope
  runApp(
    UncontrolledProviderScope(container: container, child: const MainApp()),
  );
}

class MainApp extends ConsumerWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch theme providers
    final themeMode = ref.watch(themeModeProvider);
    final lightTheme = ref.watch(lightThemeProvider);
    final darkTheme = ref.watch(darkThemeProvider);

    // Watch the auth process state
    final authProcess = ref.watch(authProcessProvider);

    // Watch the Firebase user provider to get authentication state
    final authState = ref.watch(firebaseUserProvider);

    // Check if user is already signed in
    final isSignedIn = ref.watch(isSignedInProvider);

    // Watch the auth listener clear provider to ensure listeners are cleared on user change
    // This is important to avoid permission issues with listeners from previous users
    // Use the ensure provider which guarantees early initialization
    ref.watch(ensureAuthListenerClearProvider);

    // Initialize RevenueCat when the user is signed in
    // This ensures RevenueCat is configured with the user ID
    ref.watch(ensureRevenueCatInitializedProvider);

    // Initialize app lifecycle service for real-time user tier updates
    // This ensures user tier is refreshed when app resumes from background
    ref.watch(ensureAppLifecycleServiceProvider);

    // If signing out, show a dedicated signing out screen
    if (authProcess == AuthProcess.signingOut) {
      return MaterialApp(
        title: 'Noeji',
        debugShowCheckedModeBanner: false,
        theme: lightTheme,
        darkTheme: darkTheme,
        themeMode: themeMode,
        home: const Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Signing out...'),
              ],
            ),
          ),
        ),
      );
    }

    // Show a loading indicator while we're determining the auth state
    return authState.when(
      loading: () {
        // Check if we can determine the auth state synchronously
        final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
        final isUserSignedIn = currentUser != null;

        Logger.debug(
          'Initial auth check (sync): User is ${isUserSignedIn ? 'signed in' : 'not signed in'}',
        );

        // If we can determine the auth state, use it
        if (isUserSignedIn) {
          return _buildApp(
            context,
            ref,
            true,
            lightTheme,
            darkTheme,
            themeMode,
          );
        }

        // Otherwise show loading indicator
        return MaterialApp(
          title: 'Noeji',
          debugShowCheckedModeBanner: false,
          theme: lightTheme,
          darkTheme: darkTheme,
          themeMode: themeMode,
          home: const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          ),
        );
      },
      error: (error, stackTrace) {
        // If there's an error, log it and show the welcome screen
        Logger.error('Error loading auth state', error);
        return _buildApp(context, ref, false, lightTheme, darkTheme, themeMode);
      },
      data: (_) {
        // Auth state loaded successfully, build the app with the current sign-in state
        return _buildApp(
          context,
          ref,
          isSignedIn,
          lightTheme,
          darkTheme,
          themeMode,
        );
      },
    );
  }

  // Helper method to build the app with the current auth state
  Widget _buildApp(
    BuildContext context,
    WidgetRef ref,
    bool isSignedIn,
    ThemeData lightTheme,
    ThemeData darkTheme,
    ThemeMode themeMode,
  ) {
    // Create the base MaterialApp
    final materialApp = MaterialApp(
      title: 'Noeji',
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: themeMode,
      // Define home route based on authentication state
      // For signed-in users, use UserStateRouter to handle 4-state monetization
      // For non-signed-in users, show WelcomeScreen
      home: isSignedIn ? const UserStateRouter() : const WelcomeScreen(),

      // Define all routes in onGenerateRoute to avoid null check issues
      onGenerateRoute: (settings) {
        // Handle all routes here to avoid null check issues
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(
              builder:
                  (context) =>
                      isSignedIn
                          ? const UserStateRouter()
                          : const WelcomeScreen(),
            );
          case '/ideabooks':
            return MaterialPageRoute(
              builder:
                  (context) =>
                      isSignedIn
                          ? const UserStateRouter()
                          : const WelcomeScreen(),
            );
          case '/welcome':
            return MaterialPageRoute(
              builder: (context) => const WelcomeScreen(),
            );
          default:
            // If route is not recognized, redirect to home
            return MaterialPageRoute(
              builder:
                  (context) =>
                      isSignedIn
                          ? const UserStateRouter()
                          : const WelcomeScreen(),
            );
        }
      },
    );

    // Wrap the app with PermissionErrorOverlay to handle permission errors
    final appWithErrorOverlay = PermissionErrorOverlay(child: materialApp);

    // Wrap with ShowCaseWidget only if the user is signed in
    // This ensures we don't show the showcase on the welcome screen
    if (isSignedIn) {
      Logger.debug('User is signed in, wrapping app with ShowCaseWidget');
      return ShowCaseWidget(
        builder: (context) => appWithErrorOverlay,
        onStart: (index, key) {
          Logger.debug('Showcase started: index=$index, key=$key');
        },
        onComplete: (index, key) {
          Logger.debug('Showcase completed: index=$index, key=$key');

          // Check if the completed showcase was the first ideabook mic button tour
          if (key == ShowcaseKeys.firstIdeabookMicButton) {
            Logger.debug(
              'First ideabook mic button tour completed, triggering color system tour',
            );

            // Update the provider to indicate that the first ideabook tour has been completed
            // This will be picked up by the IdeabooksListScreen to show the color system tour
            ref.read(firstIdeabookTourCompletedProvider.notifier).state = true;
          }
        },
        onFinish: () {
          Logger.debug('Showcase tour finished');
        },
        // Enable auto play for debugging
        autoPlay: false,
        // Reduced blur effect for better visibility
        blurValue:
            0.5, // Reduced from 2 to 0.5 to make the background less blurry
      );
    } else {
      Logger.debug(
        'User is not signed in, not wrapping app with ShowCaseWidget',
      );
    }

    // Return the app with error overlay for non-signed-in users
    return appWithErrorOverlay;
  }
}
