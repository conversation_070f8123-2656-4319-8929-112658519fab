import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/noeji_pro_logo.dart';
import 'package:noeji/utils/logger.dart';

/// Custom paywall screen for new users
/// This screen is non-dismissible and shows subscription options with custom UI
class CustomPaywallScreen extends ConsumerStatefulWidget {
  const CustomPaywallScreen({super.key});

  @override
  ConsumerState<CustomPaywallScreen> createState() => _CustomPaywallScreenState();
}

class _CustomPaywallScreenState extends ConsumerState<CustomPaywallScreen> {
  Offerings? _offerings;
  Package? _selectedPackage;
  bool _isLoading = true;
  bool _isPurchasing = false;

  @override
  void initState() {
    super.initState();
    _fetchOfferings();
  }

  Future<void> _fetchOfferings() async {
    try {
      Logger.debug('CustomPaywallScreen: Fetching offerings from RevenueCat');
      final offerings = await Purchases.getOfferings();
      
      if (offerings.current != null && offerings.current!.availablePackages.isNotEmpty) {
        setState(() {
          _offerings = offerings;
          // Default to the first available package
          _selectedPackage = offerings.current!.availablePackages.first;
          _isLoading = false;
        });
        Logger.debug('CustomPaywallScreen: Successfully loaded ${offerings.current!.availablePackages.length} packages');
      } else {
        Logger.debug('CustomPaywallScreen: No offerings available');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      Logger.error('CustomPaywallScreen: Error fetching offerings', e);
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Make the screen non-dismissible
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: _isLoading
            ? _buildLoadingScreen()
            : _buildPaywallContent(),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading subscription options...',
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildPaywallContent() {
    if (_offerings == null || _offerings!.current == null) {
      return _buildErrorScreen();
    }

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Spacer(flex: 1),

            // Noeji Logo
            Center(
              child: NoejiProLogo(height: 50),
            ),
            const SizedBox(height: 32),

            // Benefits List
            _buildBenefitsList(),
            const SizedBox(height: 24),

            // Package Selector
            if (_offerings!.current!.availablePackages.isNotEmpty) ...[
              _buildPackageSelector(),
              const SizedBox(height: 24),
            ],

            // Call to Action Button
            _buildCallToActionButton(),
            const SizedBox(height: 12),

            // Auto-renew text
            _buildAutoRenewText(),
            const SizedBox(height: 20),

            // Footer with all links in one line
            _buildFooter(),
            const Spacer(flex: 1),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: NoejiTheme.colorsOf(context).error,
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to load subscription options',
              style: NoejiTheme.textStylesOf(context).titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your internet connection and try again.',
              style: NoejiTheme.textStylesOf(context).bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            OutlinedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                });
                _fetchOfferings();
              },
              child: Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitsList() {
    final benefits = [
      {
        'primary': 'Unlimited access to all features',
        'secondary': '',
      },
      {
        'primary': 'Full control over AI',
        'secondary': 'Adjust tone, verbosity, and even system prompt.',
      },
      {
        'primary': 'Customize app behavior and look',
        'secondary': 'Enable Cloud Sync, custom color themes and more.',
      },
      {
        'primary': 'Priority access to new features',
        'secondary': 'E.g. collaborative ideabook is coming soon.',
      },
      {
        'primary': 'No commitment, cancel anytime',
        'secondary': '',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: benefits.map((benefit) => _BenefitItem(
        primaryText: benefit['primary']!,
        secondaryText: benefit['secondary']!,
      )).toList(),
    );
  }

  Widget _buildPackageSelector() {
    final packages = _offerings!.current!.availablePackages;

    if (packages.length == 1) {
      // If only one package, just show it selected
      return _buildPackageCard(packages.first, true);
    }

    return Column(
      children: packages.map((package) {
        final isSelected = package == _selectedPackage;
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: GestureDetector(
            onTap: () => setState(() => _selectedPackage = package),
            child: _buildPackageCard(package, isSelected),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPackageCard(Package package, bool isSelected) {
    final product = package.storeProduct;
    final isYearly = product.identifier.toLowerCase().contains('yearly') ||
                     product.identifier.toLowerCase().contains('annual');

    // Calculate pricing display
    String priceDisplay;
    String billingPeriod;

    if (isYearly) {
      // For yearly: show monthly equivalent with strikethrough if discounted
      final yearlyPrice = product.price;
      final monthlyEquivalent = yearlyPrice / 12;
      final regularMonthlyPrice = 5.99; // Assuming regular monthly is $5.99

      if (monthlyEquivalent < regularMonthlyPrice) {
        priceDisplay = '\$${monthlyEquivalent.toStringAsFixed(2)} / mo';
        billingPeriod = 'billed yearly';
      } else {
        priceDisplay = product.priceString;
        billingPeriod = 'billed yearly';
      }
    } else {
      priceDisplay = '${product.priceString} / mo';
      billingPeriod = 'billed monthly';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected
            ? NoejiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.05)
            : Colors.transparent,
        border: Border.all(
          color: isSelected
              ? NoejiTheme.colorsOf(context).textPrimary
              : NoejiTheme.colorsOf(context).border,
          width: isSelected ? 2.0 : 1.0,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isYearly ? 'Annual' : 'Monthly',
                  style: GoogleFonts.afacad(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  priceDisplay,
                  style: GoogleFonts.afacad(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  billingPeriod,
                  style: GoogleFonts.afacad(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: NoejiTheme.colorsOf(context).textSecondary,
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            Icon(
              Icons.check_circle,
              color: NoejiTheme.colorsOf(context).textPrimary,
              size: 24,
            ),
        ],
      ),
    );
  }

  String _getPeriodName(String period) {
    switch (period.toLowerCase()) {
      case 'day':
        return 'day';
      case 'week':
        return 'week';
      case 'month':
        return 'month';
      case 'year':
        return 'year';
      default:
        return period.toLowerCase();
    }
  }

  Widget _buildCallToActionButton() {
    if (_selectedPackage == null) return const SizedBox.shrink();

    final product = _selectedPackage!.storeProduct;
    final hasFreeTrial = product.introductoryPrice != null;

    String ctaText;
    if (hasFreeTrial) {
      final trialPeriod = product.introductoryPrice!.periodNumberOfUnits;
      final periodName = _getPeriodName(product.introductoryPrice!.period);
      final pluralPeriod = trialPeriod > 1 ? '${periodName}s' : periodName;
      ctaText = 'Try FREE for $trialPeriod $pluralPeriod';
    } else {
      ctaText = 'Subscribe Now';
    }

    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: NoejiTheme.colorsOf(context).textPrimary,
          foregroundColor: Theme.of(context).scaffoldBackgroundColor,
          side: BorderSide(
            color: NoejiTheme.colorsOf(context).textPrimary,
            width: 2,
          ),
        ),
        onPressed: _isPurchasing ? null : _purchase,
        child: _isPurchasing
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).scaffoldBackgroundColor,
                  ),
                ),
              )
            : Text(
                ctaText,
                style: GoogleFonts.afacad(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildAutoRenewText() {
    if (_selectedPackage == null) return const SizedBox.shrink();

    final product = _selectedPackage!.storeProduct;
    final hasFreeTrial = product.introductoryPrice != null;

    if (!hasFreeTrial) {
      return Text(
        'Auto-renews at ${product.priceString}',
        textAlign: TextAlign.center,
        style: GoogleFonts.afacad(
          fontSize: 14,
          color: NoejiTheme.colorsOf(context).textSecondary,
        ),
      );
    }

    return Text(
      'Then ${product.priceString} after trial ends',
      textAlign: TextAlign.center,
      style: GoogleFonts.afacad(
        fontSize: 14,
        color: NoejiTheme.colorsOf(context).textSecondary,
      ),
    );
  }

  Widget _buildFooter() {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 8,
      children: [
        TextButton(
          onPressed: _isPurchasing ? null : _restorePurchases,
          child: Text(
            'Restore Purchase',
            style: GoogleFonts.afacad(
              fontSize: 14,
              decoration: TextDecoration.underline,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
        Text(
          '•',
          style: GoogleFonts.afacad(
            fontSize: 14,
            color: NoejiTheme.colorsOf(context).textSecondary,
          ),
        ),
        TextButton(
          onPressed: () => _launchUrl('https://noeji.com/terms'),
          child: Text(
            'Terms of Service',
            style: GoogleFonts.afacad(
              fontSize: 14,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
        Text(
          '•',
          style: GoogleFonts.afacad(
            fontSize: 14,
            color: NoejiTheme.colorsOf(context).textSecondary,
          ),
        ),
        TextButton(
          onPressed: () => _launchUrl('https://noeji.com/privacy'),
          child: Text(
            'Privacy Policy',
            style: GoogleFonts.afacad(
              fontSize: 14,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  // Purchase and restore methods
  Future<void> _purchase() async {
    if (_selectedPackage == null) return;

    setState(() => _isPurchasing = true);
    Logger.debug('CustomPaywallScreen: Starting purchase for package: ${_selectedPackage!.identifier}');

    try {
      await Purchases.purchasePackage(_selectedPackage!);
      Logger.debug('CustomPaywallScreen: Purchase successful');

      // The UserStateNotifier will automatically detect the change and navigate
      // No need to manually navigate here

    } catch (e) {
      Logger.error('CustomPaywallScreen: Purchase failed', e);

      // Check if user cancelled the purchase
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('cancelled') || errorString.contains('cancel')) {
        Logger.debug('CustomPaywallScreen: User cancelled purchase');
        // User cancelled, no need to show error
      } else {
        _showErrorSnackBar('Purchase failed. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() => _isPurchasing = false);
      }
    }
  }

  Future<void> _restorePurchases() async {
    setState(() => _isPurchasing = true);
    Logger.debug('CustomPaywallScreen: Starting restore purchases');

    try {
      final customerInfo = await Purchases.restorePurchases();
      Logger.debug('CustomPaywallScreen: Restore purchases successful');

      // Check if any entitlements were restored
      if (customerInfo.entitlements.active.isNotEmpty) {
        _showSuccessSnackBar('Purchases restored successfully!');
        // The UserStateNotifier will automatically detect the change and navigate
      } else {
        _showErrorSnackBar('No previous purchases found to restore.');
      }

    } catch (e) {
      Logger.error('CustomPaywallScreen: Restore purchases failed', e);
      _showErrorSnackBar('Could not restore purchases. Please try again.');
    } finally {
      if (mounted) {
        setState(() => _isPurchasing = false);
      }
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        Logger.error('CustomPaywallScreen: Could not launch URL: $url');
      }
    } catch (e) {
      Logger.error('CustomPaywallScreen: Error launching URL: $url', e);
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.afacad(color: Colors.white),
        ),
        backgroundColor: NoejiTheme.colorsOf(context).error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.afacad(color: Colors.white),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Widget for displaying a single benefit item with checkmark
class _BenefitItem extends StatelessWidget {
  final String primaryText;
  final String secondaryText;

  const _BenefitItem({
    required this.primaryText,
    required this.secondaryText,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 18,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  primaryText,
                  style: GoogleFonts.afacad(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
                if (secondaryText.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    secondaryText,
                    style: GoogleFonts.afacad(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: NoejiTheme.colorsOf(context).textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
