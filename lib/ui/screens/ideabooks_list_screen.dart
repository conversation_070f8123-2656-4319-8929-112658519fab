import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/services/tour/tour_service.dart';

import 'package:noeji/ui/providers/color_filter_provider.dart';
import 'package:noeji/ui/providers/combined_filter_provider.dart';
import 'package:noeji/ui/providers/combined_ideabook_provider.dart';
import 'package:noeji/ui/providers/first_ideabook_created_provider.dart';
import 'package:noeji/ui/providers/group_provider.dart';
import 'package:noeji/ui/providers/ideabook_color_provider.dart';
import 'package:noeji/ui/providers/ideabook_provider.dart';
import 'package:noeji/ui/providers/ideabook_swipe_provider.dart';
import 'package:noeji/ui/providers/sort_provider.dart';
import 'package:noeji/ui/providers/tour_completion_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/bottom_panel_controller.dart';
import 'package:noeji/ui/widgets/context_menu.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/ui/widgets/ideabook_list_item.dart';
import 'package:noeji/ui/widgets/animated_hamburger_button.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';

/// Provider to track whether the hamburger menu is open
final isMenuOpenProvider = StateProvider<bool>((ref) => false);

/// Main screen showing the list of ideabooks
class IdeabooksListScreen extends ConsumerStatefulWidget {
  /// Constructor
  const IdeabooksListScreen({super.key});

  @override
  ConsumerState<IdeabooksListScreen> createState() =>
      _IdeabooksListScreenState();
}

class _IdeabooksListScreenState extends ConsumerState<IdeabooksListScreen> {
  /// ScrollController for the ideabooks list
  /// Using keepScrollOffset: true to maintain position during rebuilds
  final ScrollController _scrollController = ScrollController(
    keepScrollOffset: true,
  );

  /// Flag to indicate if we should scroll to the newly created ideabook on the next frame
  bool _shouldScrollToNewIdeabook = false;

  /// Store the controller reference as a class field
  FirestoreListenerController? _firestoreController;

  @override
  void initState() {
    super.initState();

    Logger.debug('IdeabooksListScreen initState called');

    // Start listening to Firestore updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Start Firestore listener
        Logger.debug('Starting Firestore listener in IdeabooksListScreen');
        _firestoreController = ref.read(
          firestoreListenerControllerProvider.notifier,
        );
        _firestoreController?.startListening();

        // Check if we need to scroll to the new ideabook
        if (_shouldScrollToNewIdeabook) {
          _scrollToNewIdeabook();
          _shouldScrollToNewIdeabook = false;
        }

        // Initialize the showcase tour
        Logger.debug('Initializing showcase tour in IdeabooksListScreen');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          try {
            // Use the TourService to show the ideabooks list tour
            Future.delayed(const Duration(milliseconds: 500), () async {
              if (mounted) {
                final shown = await TourService.showIdeabooksListTour(context);
                Logger.debug(
                  'Ideabooks list tour shown from IdeabooksListScreen: $shown',
                );
              }
            });
          } catch (e) {
            Logger.error(
              'Error initializing showcase tour in IdeabooksListScreen',
              e,
            );
          }
        });
      }
    });

    // Listen to the newIdeabookCreatedProvider
    ref.listenManual(newIdeabookCreatedProvider, (previous, next) {
      if (next == true) {
        // Reset the provider flag
        ref.read(newIdeabookCreatedProvider.notifier).state = false;

        // Set our internal flag to scroll on the next frame
        _shouldScrollToNewIdeabook = true;

        // Schedule the scroll operation with a delay to ensure the list is updated
        Future.delayed(const Duration(milliseconds: 50), () {
          _scrollToNewIdeabook();

          // Reset our flag after a delay to ensure the scroll operation completes
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              _shouldScrollToNewIdeabook = false;
            }
          });
        });
      }
    });

    // Listen to the firstIdeabookCreatedProvider
    Logger.debug(
      'Setting up listener for firstIdeabookCreatedProvider in IdeabooksListScreen',
    );
    ref.listenManual(firstIdeabookCreatedProvider, (previous, next) {
      Logger.debug(
        'firstIdeabookCreatedProvider changed from $previous to $next',
      );
      if (next == true) {
        // Reset the provider flag
        Logger.debug('Resetting firstIdeabookCreatedProvider to false');
        ref.read(firstIdeabookCreatedProvider.notifier).reset();

        // Check if we should show the first ideabook tour
        if (mounted) {
          Logger.debug(
            'First ideabook created, checking if we should show the tour',
          );

          // Add a delay to ensure the UI is fully rendered with the new ideabook
          Future.delayed(const Duration(milliseconds: 800), () {
            if (!mounted) {
              Logger.debug(
                'Widget no longer mounted after delay, aborting tour',
              );
              return;
            }

            // Use the TourService to show the first ideabook tour
            if (mounted) {
              TourService.showFirstIdeabookTour(context).then((shown) {
                Logger.debug('First ideabook tour shown: $shown');
              });
            }
          });
        } else {
          Logger.debug('Widget not mounted, cannot show first ideabook tour');
        }
      }
    });

    // Note: We're using combinedIdeabooksNotifierProvider indirectly through combinedFilteredIdeabooksProvider

    // Listen to the ideabooks list to detect changes
    ref.listenManual(combinedFilteredIdeabooksProvider, (previous, next) {
      if (previous != null && next.hasValue && previous.hasValue) {
        final prevList = previous.value!;
        final nextList = next.value!;

        // If the list grew, we might need to scroll
        if (nextList.length > prevList.length && _shouldScrollToNewIdeabook) {
          // Schedule a scroll after the frame is rendered
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToNewIdeabook();
            _shouldScrollToNewIdeabook = false;
          });
        }

        // Check if this is the first ideabook (list went from 0 to 1)
        if (prevList.isEmpty && nextList.length == 1) {
          Logger.debug(
            'First ideabook detected! Triggering first ideabook tour',
          );

          // Add a delay to ensure the UI is fully rendered with the new ideabook
          Future.delayed(const Duration(milliseconds: 800), () {
            if (!mounted) return;

            // Use the TourService to show the first ideabook tour
            if (mounted) {
              TourService.showFirstIdeabookTour(context).then((shown) {
                Logger.debug(
                  'First ideabook tour shown from ideabooks list change: $shown',
                );
              });
            }
          });
        }
      }
    });

    // Listen to the group by color provider to detect changes
    ref.listenManual(groupByColorProvider, (previous, next) {
      Logger.debug(
        'Group by color changed from $previous to $next in IdeabooksListScreen',
      );

      // Force a refresh of the combined filtered ideabooks provider
      ref.invalidate(combinedFilteredIdeabooksProvider);

      // Force a rebuild of the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild
        });
      }
    });

    // Listen to the first ideabook tour completion provider
    ref.listenManual(firstIdeabookTourCompletedProvider, (previous, next) {
      Logger.debug(
        'First ideabook tour completed changed from $previous to $next',
      );

      if (next == true && mounted) {
        // Reset the provider state
        ref.read(firstIdeabookTourCompletedProvider.notifier).state = false;

        // Show the color system tour
        _showColorSystemTour();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if we need to scroll after dependencies change
    // but don't reset the flag here - let the scroll operation handle that
    if (_shouldScrollToNewIdeabook) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToNewIdeabook();
      });
    }
  }

  @override
  void dispose() {
    // Stop listening to Firestore updates if using Firestore
    try {
      if (_firestoreController != null) {
        Logger.debug('Stopping Firestore listener in IdeabooksListScreen');
        // Use the stored controller reference instead of accessing ref
        final controller = _firestoreController;
        // Schedule the stopListening call for the next frame to avoid dispose conflicts
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller?.stopListening();
        });
      }
    } catch (e) {
      Logger.error(
        'Error stopping Firestore listener in IdeabooksListScreen: $e',
      );
    }

    // Clear the reference
    _firestoreController = null;
    _scrollController.dispose();
    super.dispose();
  }

  /// Scroll to the appropriate position based on sort order
  void _scrollToNewIdeabook() {
    // Add a delay to ensure the list is fully rendered with the new item
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!mounted || !_scrollController.hasClients) return;

      try {
        // Get the current list of ideabooks
        final ideabooksAsync = ref.read(combinedFilteredIdeabooksProvider);
        // Get the current sort order
        final sortOrder = ref.read(ideabookSortOrderProvider);

        // Only proceed if we have data
        if (ideabooksAsync.hasValue) {
          final ideabooks = ideabooksAsync.value!;

          // If sort order is descending (latest at top), scroll to top
          if (sortOrder == SortOrder.descending) {
            Logger.debug(
              'Scrolling to top of list with ${ideabooks.length} ideabooks (latest at top)',
            );

            // Scroll directly to the top without bounce effect
            _scrollController.animateTo(
              0, // Scroll to the top
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          } else {
            // For ascending order (oldest at top), scroll to bottom
            Logger.debug(
              'Scrolling to bottom of list with ${ideabooks.length} ideabooks (oldest at top)',
            );

            // Calculate the maximum scroll extent
            final targetPosition = _scrollController.position.maxScrollExtent;

            // Scroll directly to the bottom without bounce effect
            _scrollController.animateTo(
              targetPosition,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        }
      } catch (e) {
        Logger.error('Error scrolling to appropriate position', e);
      }
    });
  }

  /// Build empty filter results widget
  Widget _buildEmptyFilterResults(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search_off, size: 48, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            'No ideabooks match your filters',
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
        ],
      ),
    );
  }

  /// Build loading indicator widget
  Widget _buildLoadingIndicator(BuildContext context, WidgetRef ref) {
    return const Center(child: CircularProgressIndicator());
  }

  /// Build error indicator widget
  Widget _buildErrorIndicator(
    BuildContext context,
    Object error,
    WidgetRef ref,
  ) {
    // Sanitize the error message
    final sanitizedError = ErrorUtils.sanitizeErrorMessage(error);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading ideabooks',
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
          const SizedBox(height: 8),
          Text(
            sanitizedError,
            style: NoejiTheme.textStylesOf(context).bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          OutlinedButton(
            onPressed: () {
              // Refresh the ideabooks
              final _ = ref.refresh(ideabooksNotifierProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Show the color system tour
  void _showColorSystemTour() {
    if (!mounted) return;

    // Add a small delay to ensure the UI is fully rendered
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted) return;

      // Use the TourService to show the color system tour
      if (mounted) {
        TourService.showIdeabookColorSystemTour(context).then((shown) {
          Logger.debug('Ideabook color system tour shown: $shown');
        });
      }
    });
  }

  /// Build empty state when there are no ideabooks
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Light bulb icon for ideas
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: NoejiTheme.colorsOf(context).textSecondary,
          ),
          const SizedBox(height: 16),
          // Main message
          Text(
            'Create an ideabook to get started',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Watch the sort order provider to ensure the widget rebuilds when it changes
    final sortOrder = ref.watch(ideabookSortOrderProvider);
    // Also watch the logger provider to ensure it's initialized
    ref.watch(sortOrderLoggerProvider);

    // Watch the group by color provider to ensure the widget rebuilds when it changes
    final isGroupByColorEnabled = ref.watch(groupByColorProvider);
    // Also watch the group by color logger provider to ensure it's initialized
    ref.watch(groupByColorLoggerProvider);

    Logger.debug(
      'Building IdeabooksListScreen with sort order: ${sortOrder.name}, group by color: $isGroupByColorEnabled',
    );

    final filteredIdeabooksAsync = ref.watch(combinedFilteredIdeabooksProvider);
    final colorFilter = ref.watch(colorFilterProvider);
    final isFilterActive = ref.watch(isFilterActiveProvider);
    final isColorPicking = ref.watch(colorPickingIdeabookIdProvider) != null;
    final isSwipeActive = ref.watch(swipedIdeabookIdProvider) != null;

    return GestureDetector(
      // Exit color picking mode, swipe mode, or recording mode when tapping outside
      onTap: () {
        if (isColorPicking) {
          ref.read(colorPickingIdeabookIdProvider.notifier).state = null;
        }
        if (isSwipeActive) {
          ref.read(swipedIdeabookIdProvider.notifier).state = null;
        }
        // Close menu if it's open
        final isMenuOpen = ref.read(isMenuOpenProvider);
        if (isMenuOpen) {
          ref.read(isMenuOpenProvider.notifier).state = false;
          Navigator.of(context).pop();
        }
        // We don't exit recording mode when tapping outside
        // as that would interrupt the recording process
      },
      child: Scaffold(
        appBar: AppBar(
          title: Consumer(
            builder: (context, ref, child) {
              // Watch the user state to determine the title
              final userStateAsync = ref.watch(realtimeUserStateProvider);

              return userStateAsync.when(
                loading: () => SvgPicture.asset(
                  'assets/images/noeji_logo_v3.svg',
                  height: 32,
                  colorFilter: ColorFilter.mode(
                    NoejiTheme.colorsOf(context).textPrimary,
                    BlendMode.srcIn,
                  ),
                ),
                error: (error, stackTrace) => SvgPicture.asset(
                  'assets/images/noeji_logo_v3.svg',
                  height: 32,
                  colorFilter: ColorFilter.mode(
                    NoejiTheme.colorsOf(context).textPrimary,
                    BlendMode.srcIn,
                  ),
                ),
                data: (userState) {
                  // Show special title for free users
                  if (userState == UserState.freeUser) {
                    return Text(
                      'Upgrade to Noeji PRO to unlock all features',
                      style: GoogleFonts.afacad(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: NoejiTheme.colorsOf(context).textPrimary,
                      ),
                    );
                  }

                  // Default logo for other user states
                  return SvgPicture.asset(
                    'assets/images/noeji_logo_v3.svg',
                    height: 32,
                    colorFilter: ColorFilter.mode(
                      NoejiTheme.colorsOf(context).textPrimary,
                      BlendMode.srcIn,
                    ),
                  );
                },
              );
            },
          ),
          centerTitle: false,
          elevation: 0,
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 12.0),
              child: Consumer(
                builder: (context, ref, child) {
                  final isMenuOpen = ref.watch(isMenuOpenProvider);

                  return AnimatedHamburgerButton(
                    isOpen: isMenuOpen,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                    onPressed: () {
                      if (isMenuOpen) {
                        // Close the menu
                        ref.read(isMenuOpenProvider.notifier).state = false;
                        Navigator.of(context).pop();
                      } else {
                        // Open the menu
                        ref.read(isMenuOpenProvider.notifier).state = true;
                        showDialog(
                          context: context,
                          builder: (context) => Dialog(
                            insetPadding: EdgeInsets.only(
                              top: 60,
                              right: 10,
                              left: MediaQuery.of(context).size.width - 260,
                            ),
                            alignment: Alignment.topRight,
                            elevation: 0,
                            backgroundColor: Colors.transparent,
                            child: const ContextMenu(),
                          ),
                        ).then((_) {
                          // Reset menu state when dialog is dismissed
                          ref.read(isMenuOpenProvider.notifier).state = false;
                        });
                      }
                    },
                  );
                },
              ),
            ),
          ],
        ),
        body: filteredIdeabooksAsync.when(
          loading: () => _buildLoadingIndicator(context, ref),
          error: (error, _) => _buildErrorIndicator(context, error, ref),
          data: (ideabooks) {
            // Show empty state when there are no ideabooks and no filter is active
            if (ideabooks.isEmpty && !isFilterActive) {
              return _buildEmptyState(context);
            }

            // Show empty filter results when there are no ideabooks but filter is active
            if (ideabooks.isEmpty && isFilterActive) {
              return Column(
                children: [
                  // Filter results indicator (only show for color filter)
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child:
                        colorFilter != null
                            ? Padding(
                              key: const ValueKey('filter-results'),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),
                              child: Row(
                                children: [
                                  // Show color filter info
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'Color: ',
                                        style:
                                            NoejiTheme.textStylesOf(
                                              context,
                                            ).bodySmall,
                                      ),
                                      Container(
                                        width: 16,
                                        height: 16,
                                        margin: const EdgeInsets.only(right: 4),
                                        decoration: BoxDecoration(
                                          color: NoejiTheme.getIdeabookColor(
                                            context,
                                            colorFilter.index,
                                          ),
                                          border: Border.all(
                                            color:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).border,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                      // Clear button
                                      GestureDetector(
                                        onTap: () {
                                          // Clear color filter
                                          ref
                                              .read(
                                                colorFilterProvider.notifier,
                                              )
                                              .state = null;
                                        },
                                        child: Container(
                                          margin: const EdgeInsets.only(
                                            left: 4,
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 6,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).searchBarBackground,
                                            border: Border.all(
                                              color:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).border,
                                              width: 1,
                                            ),
                                          ),
                                          child: Text(
                                            'Clear',
                                            style:
                                                NoejiTheme.textStylesOf(
                                                  context,
                                                ).bodySmall,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const Spacer(),
                                  Text(
                                    '${ideabooks.length} found',
                                    style:
                                        NoejiTheme.textStylesOf(
                                          context,
                                        ).bodySmall,
                                  ),
                                ],
                              ),
                            )
                            : const SizedBox(
                              key: ValueKey('no-filter-results'),
                              height: 0,
                            ),
                  ),

                  Expanded(child: _buildEmptyFilterResults(context, ref)),
                ],
              );
            }

            // Use a CustomScrollView with SliverList
            return CustomScrollView(
              controller: _scrollController,
              // Always allow scrolling with platform-specific bounce effect, even when content is short
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                // Filter results indicator (only show for color filter)
                SliverToBoxAdapter(
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child:
                        colorFilter != null
                            ? Padding(
                              key: const ValueKey('filter-results'),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),
                              child: Row(
                                children: [
                                  // Show color filter info
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'Color: ',
                                        style:
                                            NoejiTheme.textStylesOf(
                                              context,
                                            ).bodySmall,
                                      ),
                                      Container(
                                        width: 16,
                                        height: 16,
                                        margin: const EdgeInsets.only(right: 4),
                                        decoration: BoxDecoration(
                                          color: NoejiTheme.getIdeabookColor(
                                            context,
                                            colorFilter.index,
                                          ),
                                          border: Border.all(
                                            color:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).border,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                      // Clear button
                                      GestureDetector(
                                        onTap: () {
                                          // Clear color filter
                                          ref
                                              .read(
                                                colorFilterProvider.notifier,
                                              )
                                              .state = null;
                                        },
                                        child: Container(
                                          margin: const EdgeInsets.only(
                                            left: 4,
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 6,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).searchBarBackground,
                                            border: Border.all(
                                              color:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).border,
                                              width: 1,
                                            ),
                                          ),
                                          child: Text(
                                            'Clear',
                                            style:
                                                NoejiTheme.textStylesOf(
                                                  context,
                                                ).bodySmall,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const Spacer(),
                                  Text(
                                    '${ideabooks.length} found',
                                    style:
                                        NoejiTheme.textStylesOf(
                                          context,
                                        ).bodySmall,
                                  ),
                                ],
                              ),
                            )
                            : const SizedBox(
                              key: ValueKey('no-filter-results'),
                              height: 0,
                            ),
                  ),
                ),

                // Ideabooks list
                SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    return Column(
                      children: [
                        IdeabookListItem(
                          key: ValueKey('ideabook-${ideabooks[index].id}'),
                          ideabook: ideabooks[index],
                        ),
                        // Add divider after each item except the last one
                        if (index < ideabooks.length - 1)
                          Divider(
                            height: 1,
                            thickness: 0.5,
                            color: NoejiTheme.colorsOf(context).divider,
                          ),
                      ],
                    );
                  }, childCount: ideabooks.length),
                ),

                // Add a small amount of padding at the bottom
                SliverToBoxAdapter(child: SizedBox(height: 20)),
              ],
            );
          },
        ),
        bottomNavigationBar: SafeArea(child: const BottomPanelController()),
      ),
    );
  }
}
