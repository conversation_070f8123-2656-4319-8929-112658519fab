import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:noeji/ui/widgets/account_deletion_dialog.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/chat/chat_rate_limit_service_provider.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/services/tour/tour_service.dart';
import 'package:noeji/ui/providers/auth_provider.dart';
import 'package:noeji/ui/providers/debug_pro_user_provider.dart';
import 'package:noeji/ui/providers/passcode_provider.dart';
import 'package:noeji/ui/screens/customize_system_prompt_screen.dart';
import 'package:noeji/ui/screens/passcode_screen.dart';
import 'package:noeji/ui/screens/welcome_screen.dart';
import 'package:noeji/ui/theme/theme_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/chat_style_selector.dart';
import 'package:noeji/ui/widgets/color_palette_selector.dart';
import 'package:noeji/ui/widgets/noeji_pro_logo.dart';
import 'package:noeji/utils/feature_flags.dart';
import 'package:noeji/utils/logger.dart';

/// Settings screen
class SettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  // State for Advanced Settings section
  bool _isAdvancedSettingsExpanded = false;

  /// Show the passcode change screen
  void _showChangePasscodeScreen(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      builder:
          (bottomSheetContext) => SizedBox(
            height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
            child: PasscodeScreen(
              isChangingPasscode: true,
              onSuccess: () {
                Navigator.of(
                  bottomSheetContext,
                ).pop(); // Close the passcode screen
              },
              onCancel: () {
                Navigator.of(
                  bottomSheetContext,
                ).pop(); // Close the passcode screen
              },
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get the current user
    final user = ref.watch(currentUserProvider);

    // If user is null, show error
    if (user == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Settings',
            style: GoogleFonts.afacad(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Center(
          child: Text(
            'User not signed in',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: GoogleFonts.afacad(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: NoejiTheme.colorsOf(context).textPrimary,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme section
            Text(
              'Theme',
              style: GoogleFonts.afacad(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: NoejiTheme.colorsOf(context).textPrimary,
              ),
            ),

            const SizedBox(height: 16),

            // Theme selection buttons - single three-way selector
            Consumer(
              builder: (context, ref, child) {
                final themeMode = ref.watch(themeModeProvider);

                return Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: NoejiTheme.colorsOf(context).border,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Light theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.wb_sunny,
                          label: 'Light',
                          isSelected: themeMode == ThemeMode.light,
                          onTap: () {
                            ref
                                .read(themeModeProvider.notifier)
                                .setThemeMode(ThemeMode.light);
                          },
                          showRightBorder: true,
                        ),
                      ),

                      // Dark theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.nightlight_round,
                          label: 'Dark',
                          isSelected: themeMode == ThemeMode.dark,
                          onTap: () {
                            ref
                                .read(themeModeProvider.notifier)
                                .setThemeMode(ThemeMode.dark);
                          },
                          showRightBorder: true,
                        ),
                      ),

                      // System theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.settings,
                          label: 'System',
                          isSelected: themeMode == ThemeMode.system,
                          onTap: () {
                            ref
                                .read(themeModeProvider.notifier)
                                .setThemeMode(ThemeMode.system);
                          },
                          showRightBorder: false,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Change passcode button
            Consumer(
              builder: (context, ref, child) {
                final isPasscodeSet =
                    ref.watch(passcodeSetCachedProvider).isSet;

                // Only show the option if a passcode is set
                if (isPasscodeSet) {
                  return Column(
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border.all(
                            color: NoejiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                        ),
                        child: InkWell(
                          onTap: () {
                            _showChangePasscodeScreen(context);
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.password,
                                  color:
                                      NoejiTheme.colorsOf(context).textPrimary,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Change Ideabook Passcode',
                                  style: GoogleFonts.afacad(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color:
                                        NoejiTheme.colorsOf(
                                          context,
                                        ).textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 40),
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),

            // Upgrade to Noeji PRO button (only for free users) - moved below theme
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userTierAsync = ref.watch(realtimeUserTierProvider);

                // If signing out, show loading state instead of tier-dependent UI
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink(); // Hide the upgrade button during sign out
                }

                return userTierAsync.when(
                  data: (userTier) {
                    // Only show for free users
                    if (userTier == 'free') {
                      return Column(
                        children: [
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                            ),
                            child: InkWell(
                              onTap: () async {
                                try {
                                  // Show the RevenueCat paywall
                                  final result =
                                      await RevenueCatUI.presentPaywall();

                                  switch (result) {
                                    case PaywallResult.purchased:
                                      Logger.debug(
                                        'User purchased subscription',
                                      );
                                      // Refresh user tier
                                      try {
                                        final refreshUserTier = ref.read(
                                          refreshUserTierProvider,
                                        );
                                        await refreshUserTier();
                                        Logger.debug(
                                          'User tier refreshed successfully',
                                        );

                                        // Also invalidate real-time providers to force immediate UI updates
                                        Logger.debug(
                                          'Settings: Invalidating real-time providers after purchase',
                                        );
                                        ref.invalidate(
                                          userTierNotifierProvider,
                                        );
                                        ref.invalidate(
                                          realtimeUserTierProvider,
                                        );
                                        ref.invalidate(
                                          realtimeIsProUserProvider,
                                        );
                                      } catch (e) {
                                        Logger.error(
                                          'Failed to refresh user tier after purchase',
                                          e,
                                        );
                                      }

                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Welcome to Noeji Pro! 🎉',
                                              style: GoogleFonts.afacad(),
                                            ),
                                            backgroundColor: Colors.green,
                                          ),
                                        );
                                      }
                                      break;
                                    case PaywallResult.restored:
                                      Logger.debug(
                                        'User restored subscription',
                                      );
                                      // Refresh user tier
                                      try {
                                        final refreshUserTier = ref.read(
                                          refreshUserTierProvider,
                                        );
                                        await refreshUserTier();
                                        Logger.debug(
                                          'User tier refreshed successfully',
                                        );

                                        // Also invalidate real-time providers to force immediate UI updates
                                        Logger.debug(
                                          'Settings: Invalidating real-time providers after restore',
                                        );
                                        ref.invalidate(
                                          userTierNotifierProvider,
                                        );
                                        ref.invalidate(
                                          realtimeUserTierProvider,
                                        );
                                        ref.invalidate(
                                          realtimeIsProUserProvider,
                                        );
                                      } catch (e) {
                                        Logger.error(
                                          'Failed to refresh user tier after restore',
                                          e,
                                        );
                                      }

                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Subscription restored successfully! 🎉',
                                              style: GoogleFonts.afacad(),
                                            ),
                                            backgroundColor: Colors.green,
                                          ),
                                        );
                                      }
                                      break;
                                    case PaywallResult.cancelled:
                                      Logger.debug('User cancelled paywall');
                                      break;
                                    case PaywallResult.error:
                                      Logger.error('Error in paywall');
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Error showing subscription options. Please try again.',
                                              style: GoogleFonts.afacad(),
                                            ),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                      break;
                                    case PaywallResult.notPresented:
                                      Logger.error('Paywall was not presented');
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Unable to show subscription options. Please try again.',
                                              style: GoogleFonts.afacad(),
                                            ),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                      break;
                                  }
                                } catch (e) {
                                  Logger.error('Error showing paywall', e);
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Error showing subscription options: ${e.toString()}',
                                          style: GoogleFonts.afacad(),
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16.0,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Primary text: "Upgrade to Noeji PRO"
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Upgrade to ',
                                          style: GoogleFonts.afacad(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).textPrimary,
                                          ),
                                        ),
                                        const NoejiProLogo(height: 20),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    // Secondary text
                                    Text(
                                      'Unlock unlimited access to all Pro features',
                                      style: GoogleFonts.afacad(
                                        fontSize: 14,
                                        color:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 40),
                        ],
                      );
                    } else {
                      // Pro user - don't show the button
                      return const SizedBox.shrink();
                    }
                  },
                  loading:
                      () => const SizedBox.shrink(), // Don't show while loading
                  error:
                      (error, stack) =>
                          const SizedBox.shrink(), // Don't show on error
                );
              },
            ),

            // App Behavior section
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userTierAsync = ref.watch(realtimeUserTierProvider);

                // If signing out, show loading state instead of tier-dependent UI
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink(); // Hide the settings during sign out
                }

                return userTierAsync.when(
                  data: (userTier) {
                    final isProUser = userTier == 'pro';

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title - clickable for free users to trigger paywall
                        GestureDetector(
                          onTap:
                              !isProUser
                                  ? () async {
                                    try {
                                      // Show the RevenueCat paywall
                                      final result =
                                          await RevenueCatUI.presentPaywall();
                                      Logger.debug('Paywall result: $result');
                                    } catch (e) {
                                      Logger.error(
                                        'Error showing paywall from App Behavior section',
                                        e,
                                      );
                                    }
                                  }
                                  : null,
                          child: Text(
                            'App Behavior',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  isProUser
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(
                                        context,
                                      ).textPrimary.withValues(alpha: 0.5),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Send voice chat on finish setting
                        Consumer(
                          builder: (context, ref, child) {
                            final appBehavior = ref.watch(appBehaviorProvider);

                            if (!appBehavior.isLoaded) {
                              // Show loading state
                              return Opacity(
                                opacity: isProUser ? 1.0 : 0.5,
                                child: GestureDetector(
                                  onTap:
                                      !isProUser
                                          ? () async {
                                            try {
                                              // Show the RevenueCat paywall
                                              final result =
                                                  await RevenueCatUI.presentPaywall();
                                              Logger.debug(
                                                'Paywall result from disabled section: $result',
                                              );
                                            } catch (e) {
                                              Logger.error(
                                                'Error showing paywall from disabled section',
                                                e,
                                              );
                                            }
                                          }
                                          : null,
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color:
                                          Theme.of(
                                            context,
                                          ).scaffoldBackgroundColor,
                                      border: Border.all(
                                        color:
                                            NoejiTheme.colorsOf(context).border,
                                        width: 1,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 16.0,
                                        horizontal: 16.0,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Send voice chat on finish',
                                                  style: GoogleFonts.afacad(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        NoejiTheme.colorsOf(
                                                          context,
                                                        ).textPrimary,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  'Loading...',
                                                  style: GoogleFonts.afacad(
                                                    fontSize: 12,
                                                    color:
                                                        NoejiTheme.colorsOf(
                                                          context,
                                                        ).textSecondary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }

                            return Opacity(
                              opacity: isProUser ? 1.0 : 0.5,
                              child: GestureDetector(
                                onTap:
                                    !isProUser
                                        ? () async {
                                          try {
                                            // Show the RevenueCat paywall
                                            final result =
                                                await RevenueCatUI.presentPaywall();
                                            Logger.debug(
                                              'Paywall result from disabled section: $result',
                                            );
                                          } catch (e) {
                                            Logger.error(
                                              'Error showing paywall from disabled section',
                                              e,
                                            );
                                          }
                                        }
                                        : null,
                                child: Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(
                                          context,
                                        ).scaffoldBackgroundColor,
                                    border: Border.all(
                                      color:
                                          NoejiTheme.colorsOf(context).border,
                                      width: 1,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16.0,
                                      horizontal: 16.0,
                                    ),
                                    child: Column(
                                      children: [
                                        // Press-and-hold to record new idea toggle
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Press-and-hold to record new idea',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Press and hold ideabook rows to record new ideas, mic buttons are hidden',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior
                                                      .longPressToRecordNewIdea,
                                              onChanged:
                                                  isProUser
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setLongPressToRecordNewIdea(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Send voice chat on finish option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Send voice chat on finish',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Voice messages are sent automatically when recording finishes',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior
                                                      .sendVoiceChatOnFinish,
                                              onChanged:
                                                  isProUser
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setSendVoiceChatOnFinish(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Copy chat message as Markdown option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Copy chat message as Markdown',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Chat messages are copied with markdown formatting',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value: appBehavior.copyAsMarkdown,
                                              onChanged:
                                                  isProUser
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setCopyAsMarkdown(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Suggest prompts by AI option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Suggest prompts by AI',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Suggested prompts are dynamically generated by AI based on ideabook content',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior
                                                      .generativeSuggestedPrompts,
                                              onChanged:
                                                  isProUser
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setGenerativeSuggestedPrompts(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Auto lock ideabooks option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Auto lock ideabooks',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'New ideabooks are automatically locked after creation',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior.autoLockIdeabooks,
                                              onChanged:
                                                  isProUser
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setAutoLockIdeabooks(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    );
                  },
                  loading:
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'App Behavior',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 310, // Increased height for five options
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        ],
                      ),
                  error:
                      (error, stack) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'App Behavior',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 16.0,
                                horizontal: 16.0,
                              ),
                              child: Column(
                                children: [
                                  // Press-and-hold to record new idea option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Press-and-hold to record new idea',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Use mic buttons to record new ideas in ideabook rows',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Send voice chat on finish option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Send voice chat on finish',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Voice messages are added to text input for editing before sending',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Copy chat message as Markdown option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Copy chat message as Markdown',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Chat messages are copied as plain text without formatting',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Suggest prompts by AI option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Suggest prompts by AI',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Suggested prompts use a static list of pre-defined prompts',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Auto lock ideabooks option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Auto lock ideabooks',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'New ideabooks are created in unlocked state by default',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Ideabook Color Palette section
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userTierAsync = ref.watch(realtimeUserTierProvider);

                // If signing out, show loading state instead of tier-dependent UI
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink(); // Hide the color palette settings during sign out
                }

                return userTierAsync.when(
                  data: (userTier) {
                    final isProUser = userTier == 'pro';

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title - clickable for free users to trigger paywall
                        GestureDetector(
                          onTap:
                              !isProUser
                                  ? () async {
                                    try {
                                      // Show the RevenueCat paywall
                                      final result =
                                          await RevenueCatUI.presentPaywall();
                                      Logger.debug('Paywall result: $result');
                                    } catch (e) {
                                      Logger.error(
                                        'Error showing paywall from Color Palette section',
                                        e,
                                      );
                                    }
                                  }
                                  : null,
                          child: Text(
                            'Ideabook Color Palette',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  isProUser
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(
                                        context,
                                      ).textPrimary.withValues(alpha: 0.5),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Color palette selector container
                        Opacity(
                          opacity: isProUser ? 1.0 : 0.5,
                          child: GestureDetector(
                            onTap:
                                !isProUser
                                    ? () async {
                                      try {
                                        // Show the RevenueCat paywall
                                        final result =
                                            await RevenueCatUI.presentPaywall();
                                        Logger.debug(
                                          'Paywall result from disabled color palette section: $result',
                                        );
                                      } catch (e) {
                                        Logger.error(
                                          'Error showing paywall from disabled color palette section',
                                          e,
                                        );
                                      }
                                    }
                                    : null,
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                border: Border.all(
                                  color:
                                      isProUser
                                          ? NoejiTheme.colorsOf(context).border
                                          : NoejiTheme.colorsOf(
                                            context,
                                          ).border.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: IgnorePointer(
                                  ignoring: !isProUser,
                                  child: const ColorPaletteSelector(),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  loading:
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ideabook Color Palette',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        ],
                      ),
                  error:
                      (error, stack) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ideabook Color Palette',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: ColorPaletteSelector(),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Customize AI Chat Style section
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userTierAsync = ref.watch(realtimeUserTierProvider);

                // If signing out, show loading state instead of tier-dependent UI
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink(); // Hide the chat style settings during sign out
                }

                return userTierAsync.when(
                  data: (userTier) {
                    final isProUser = userTier == 'pro';

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title - clickable for free users to trigger paywall
                        GestureDetector(
                          onTap:
                              !isProUser
                                  ? () async {
                                    try {
                                      // Show the RevenueCat paywall
                                      final result =
                                          await RevenueCatUI.presentPaywall();
                                      Logger.debug('Paywall result: $result');
                                    } catch (e) {
                                      Logger.error(
                                        'Error showing paywall from Chat Style section',
                                        e,
                                      );
                                    }
                                  }
                                  : null,
                          child: Text(
                            'AI Chat Style',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  isProUser
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(
                                        context,
                                      ).textPrimary.withValues(alpha: 0.5),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Chat style selector container
                        Opacity(
                          opacity: isProUser ? 1.0 : 0.5,
                          child: GestureDetector(
                            onTap:
                                !isProUser
                                    ? () async {
                                      try {
                                        // Show the RevenueCat paywall
                                        final result =
                                            await RevenueCatUI.presentPaywall();
                                        Logger.debug(
                                          'Paywall result from disabled chat style section: $result',
                                        );
                                      } catch (e) {
                                        Logger.error(
                                          'Error showing paywall from disabled chat style section',
                                          e,
                                        );
                                      }
                                    }
                                    : null,
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                border: Border.all(
                                  color:
                                      isProUser
                                          ? NoejiTheme.colorsOf(context).border
                                          : NoejiTheme.colorsOf(
                                            context,
                                          ).border.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  children: [
                                    IgnorePointer(
                                      ignoring: !isProUser,
                                      child: const ChatStyleSelector(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  loading:
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'AI Chat Style',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 200,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        ],
                      ),
                  error:
                      (error, stackTrace) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'AI Chat Style',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                'Error loading chat style options',
                                style: GoogleFonts.afacad(
                                  color:
                                      NoejiTheme.colorsOf(
                                        context,
                                      ).textSecondary,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Customize System Prompt button
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userTierAsync = ref.watch(realtimeUserTierProvider);

                // If signing out, show loading state instead of tier-dependent UI
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink(); // Hide during sign out
                }

                return userTierAsync.when(
                  data: (userTier) {
                    final isProUser = userTier == 'pro';

                    return Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        border: Border.all(
                          color: isProUser
                              ? NoejiTheme.colorsOf(context).border
                              : NoejiTheme.colorsOf(context).border.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: InkWell(
                        onTap: isProUser
                            ? () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const CustomizeSystemPromptScreen(),
                                  ),
                                );
                              }
                            : () async {
                                try {
                                  // Show the RevenueCat paywall
                                  final result = await RevenueCatUI.presentPaywall();
                                  Logger.debug('Paywall result: $result');
                                } catch (e) {
                                  Logger.error(
                                    'Error showing paywall from Customize System Prompt button',
                                    e,
                                  );
                                }
                              },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.edit_note,
                                color: isProUser
                                    ? NoejiTheme.colorsOf(context).textPrimary
                                    : NoejiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.5),
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Customize System Prompt',
                                style: GoogleFonts.afacad(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: isProUser
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.5),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                  loading: () => Container(
                    width: double.infinity,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      border: Border.all(
                        color: NoejiTheme.colorsOf(context).border,
                        width: 1,
                      ),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  error: (error, stackTrace) => Container(
                    width: double.infinity,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      border: Border.all(
                        color: NoejiTheme.colorsOf(context).border,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'Error loading system prompt options',
                        style: GoogleFonts.afacad(
                          color: NoejiTheme.colorsOf(context).textSecondary,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Debug settings (only shown in debug mode)
            if (FeatureFlags.showDebugOptions) ...[
              Text(
                'Debug Settings',
                style: GoogleFonts.afacad(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),

              const SizedBox(height: 16),

              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Store the context before the async gap
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    // Reset all tours
                    TourService.resetAllTours().then((result) {
                      // Show a snackbar to confirm
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            result
                                ? 'Tour tooltip states cleared. Tooltips will appear again when their conditions are met.'
                                : 'Failed to clear tour tooltip states',
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.restart_alt,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Clear Tour States',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Rate limit debug status button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Debug rate limit status
                    Logger.debug('===== DEBUGGING RATE LIMIT STATUS =====');
                    ref
                        .read(chatRateLimitServiceProvider)
                        .debugRateLimitStatus()
                        .then((_) {
                          // Show a snackbar to confirm if context is still valid
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Rate limit status logged to console',
                                ),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bug_report,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Debug Rate Limit Status',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Clear rate limit logs button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Clear rate limit logs
                    Logger.debug(
                      '===== CLEARING RATE LIMIT LOGS FOR TESTING =====',
                    );
                    ref
                        .read(chatRateLimitServiceProvider)
                        .clearMessageLog()
                        .then((success) {
                          Logger.debug('Rate limit logs cleared: $success');

                          // Show a snackbar to confirm if context is still valid
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Rate limit logs cleared for testing',
                                ),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.clear_all,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Clear Rate Limit Logs',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Debug pro user toggle
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Consumer(
                    builder: (context, ref, child) {
                      final debugProUser = ref.watch(debugProUserProvider);

                      return Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Force Pro User Status',
                                  style: GoogleFonts.afacad(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: NoejiTheme.colorsOf(context).textPrimary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Enable all pro features for testing (debug only)',
                                  style: GoogleFonts.afacad(
                                    fontSize: 14,
                                    color: NoejiTheme.colorsOf(context).textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Switch(
                            value: debugProUser,
                            onChanged: (value) {
                              ref.read(debugProUserProvider.notifier).setEnabled(value);

                              // Show feedback to user
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    value
                                        ? 'Debug pro user status enabled - all pro features unlocked'
                                        : 'Debug pro user status disabled - using real subscription status',
                                  ),
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            },
                            activeColor: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Debug app limits button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () async {
                    // Debug app limits
                    Logger.debug(
                      '===== DEBUGGING APP LIMITS FROM REMOTE CONFIG =====',
                    );
                    try {
                      final appLimitsService = ref.read(
                        appLimitsServiceProvider,
                      );
                      final userTierAsync = ref.read(userTierProvider);
                      final userTier = userTierAsync.value;

                      Logger.debug('Current user tier: $userTier');
                      appLimitsService.logCurrentLimits(userTier: userTier);

                      // Show a snackbar to confirm if context is still valid
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('App limits logged to console'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    } catch (e) {
                      Logger.error('Failed to debug app limits', e);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Failed to debug app limits - check console',
                            ),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.settings_applications,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Debug App Limits',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],

            // Add some space before the Sign Out button
            const SizedBox(height: 40),

            // Sign Out button
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border.all(
                  color: NoejiTheme.colorsOf(context).border,
                  width: 1,
                ),
              ),
              child: InkWell(
                onTap: () async {
                  // Show confirmation dialog
                  final bool? confirmSignOut = await showDialog<bool>(
                    context: context,
                    builder: (BuildContext dialogContext) {
                      return AlertDialog(
                        title: Text(
                          'Sign Out',
                          style:
                              NoejiTheme.textStylesOf(dialogContext).bodyLarge,
                        ),
                        content: Text(
                          'Are you sure you want to sign out?',
                          style:
                              NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                          side: BorderSide(
                            color: NoejiTheme.colorsOf(dialogContext).border,
                            width: 1,
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed:
                                () => Navigator.of(dialogContext).pop(false),
                            child: Text(
                              'Cancel',
                              style:
                                  NoejiTheme.textStylesOf(
                                    dialogContext,
                                  ).buttonText,
                            ),
                          ),
                          TextButton(
                            onPressed:
                                () => Navigator.of(dialogContext).pop(true),
                            child: Text(
                              'Sign Out',
                              style:
                                  NoejiTheme.textStylesOf(
                                    dialogContext,
                                  ).buttonText,
                            ),
                          ),
                        ],
                      );
                    },
                  );

                  // If user confirmed, proceed with sign out
                  if (confirmSignOut == true) {
                    try {
                      final signOut = ref.read(signOutProvider);
                      await signOut();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Signed out successfully',
                              style: GoogleFonts.afacad(),
                            ),
                          ),
                        );
                      }
                    } catch (e) {
                      Logger.error('Error signing out', e);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Error signing out: ${e.toString()}',
                              style: GoogleFonts.afacad(),
                            ),
                            backgroundColor: NoejiTheme.colorsOf(context).error,
                          ),
                        );
                      }
                    }
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.logout,
                        color: NoejiTheme.colorsOf(context).textPrimary,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Sign Out',
                        style: GoogleFonts.afacad(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Advanced Settings section
            _buildAdvancedSettingsSection(context),

            const SizedBox(height: 40),

            // App User ID display at the bottom - centered
            Center(
              child: GestureDetector(
                onTap: () async {
                  try {
                    final appUserIdAsync = ref.read(appUserIdProvider);
                    final appUserId = appUserIdAsync.value;

                    if (appUserId != null) {
                      await Clipboard.setData(ClipboardData(text: appUserId));
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'App User ID copied to clipboard',
                              style: GoogleFonts.afacad(),
                            ),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    Logger.error('Error copying app user ID', e);
                  }
                },
                child: Text(
                  'App User ID: ${ref.watch(appUserIdProvider).value ?? 'Loading...'}',
                  style: GoogleFonts.afacad(
                    fontSize: 12,
                    color: NoejiTheme.colorsOf(context).textSecondary,
                  ),
                ),
              ),
            ),

            // Add some bottom padding
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// Build the Advanced Settings collapsible section
  Widget _buildAdvancedSettingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Advanced Settings header with arrow
        Container(
          width: double.infinity,
          color: Theme.of(context).scaffoldBackgroundColor,
          child: InkWell(
            onTap: () {
              setState(() {
                _isAdvancedSettingsExpanded = !_isAdvancedSettingsExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Dangerous Zone',
                    style: GoogleFonts.afacad(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  AnimatedRotation(
                    turns: _isAdvancedSettingsExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Collapsible content
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: _isAdvancedSettingsExpanded ? null : 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: _isAdvancedSettingsExpanded ? 1.0 : 0.0,
            child: _isAdvancedSettingsExpanded
                ? Column(
                    children: [
                      const SizedBox(height: 16),
                      // Delete Account button
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border.all(color: Colors.red, width: 1),
                        ),
                        child: InkWell(
                          onTap: () async {
                            // Show confirmation dialog
                            final bool confirmed =
                                await AccountDeletionDialogs.showConfirmationDialog(
                                  context,
                                );

                            // If user confirmed, proceed with account deletion
                            if (confirmed && context.mounted) {
                              await _processAccountDeletion(context);
                            }
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.delete_forever, color: Colors.red, size: 20),
                                const SizedBox(width: 12),
                                Text(
                                  'Delete Your Account and Data',
                                  style: GoogleFonts.afacad(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          ),
        ),
      ],
    );
  }

  /// Build a theme selection segment for the three-way selector
  Widget _buildThemeSegment({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required bool showRightBorder,
  }) {
    // Determine background color based on selection and theme
    Color backgroundColor;
    if (isSelected) {
      // Use a stronger contrast background for selected state
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
      backgroundColor =
          isDarkTheme
              ? NoejiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.25)
              : NoejiTheme.colorsOf(
                context,
              ).textPrimary.withValues(alpha: 0.15);
    } else {
      backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          right:
              showRightBorder
                  ? BorderSide(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  )
                  : BorderSide.none,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: NoejiTheme.colorsOf(context).textPrimary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.afacad(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Process account deletion using the new two-stage account deletion service
  Future<void> _processAccountDeletion(
    BuildContext context,
  ) async {
    if (!context.mounted) return;

    final accountDeletionService = ref.read(accountDeletionServiceProvider);
    bool reauthSuccessful = false;

    try {
      // --- STAGE 1: Prepare and Re-authenticate ---
      reauthSuccessful = await accountDeletionService
          .prepareForDeletionAndReauthenticate((
            String providerNameFriendly,
          ) async {
            // This callback is invoked by the service to show the "re-auth info" dialog
            return await AccountDeletionDialogs.showReauthenticationInfoDialog(
              context,
              providerNameFriendly,
            );
          });

      if (!reauthSuccessful) {
        // User cancelled the re-auth info dialog, or re-auth failed internally
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Account deletion process cancelled during identity verification.',
              ),
            ),
          );
        }
        return;
      }
    } catch (e) {
      // Handle exceptions specifically from prepareForDeletionAndReauthenticate
      Logger.error("Error during re-authentication preparation stage: $e");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Identity verification failed: ${e.toString().replaceFirst("Exception: ", "")}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return; // Stop the process
    }

    // --- STAGE 2: Final Deletion Confirmation ---
    if (!context.mounted) return;

    final bool confirmedFinalDelete =
        await AccountDeletionDialogs.showFinalDeletionConfirmationDialog(
          context,
        );

    if (!confirmedFinalDelete) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Account deletion cancelled.')));
      }
      return;
    }

    // --- STAGE 3: Execute Deletion ---
    if (!context.mounted) return;
    AccountDeletionDialogs.showLoadingDialog(context);

    try {
      await accountDeletionService.executeConfirmedDeletion();

      if (context.mounted) {
        AccountDeletionDialogs.hideLoadingDialog(context);

        // Navigate to welcome screen
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const WelcomeScreen()),
          (Route<dynamic> route) => false,
        );

        // Show success message
        AccountDeletionDialogs.showSuccessMessage(context);
      }
    } catch (e) {
      Logger.error("Error during final account execution stage: $e");
      if (context.mounted) {
        AccountDeletionDialogs.hideLoadingDialog(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Account deletion failed: ${e.toString().replaceFirst("Exception: ", "")}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
