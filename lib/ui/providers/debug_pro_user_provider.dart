import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/utils/logger.dart';

/// Debug provider to force pro user status for testing
/// This should only be used in debug builds for testing purposes
class DebugProUserNotifier extends StateNotifier<bool> {
  DebugProUserNotifier() : super(false);

  /// Toggle the debug pro user status
  void toggle() {
    state = !state;
    Logger.debug('Debug pro user status toggled: $state');
  }

  /// Set the debug pro user status
  void setEnabled(bool enabled) {
    state = enabled;
    Logger.debug('Debug pro user status set to: $enabled');
  }

  /// Reset to default (disabled)
  void reset() {
    state = false;
    Logger.debug('Debug pro user status reset to: false');
  }
}

/// Provider for debug pro user status
/// When enabled, this will force the app to treat the user as a pro user
/// regardless of their actual subscription status
final debugProUserProvider = StateNotifierProvider<DebugProUserNotifier, bool>((ref) {
  return DebugProUserNotifier();
});
