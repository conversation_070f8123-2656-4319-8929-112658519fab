/// Enum representing the type of content for an idea
enum ContentType {
  /// Voice recording
  voice,

  /// Text input
  text,
}

/// Enum representing the color options for ideabooks
enum IdeabookColor {
  /// No color (default)
  none,

  /// Red color
  red,

  /// Green color
  green,

  /// Blue color
  blue,

  /// Yellow color
  yellow,

  /// Purple color
  purple,

  /// Orange color
  orange,
}

/// Enum representing the 4 user states in the monetization model
enum UserState {
  /// User just signed up, has no entitlement, and has never had a trial.
  /// Must be shown a paywall before accessing the app.
  newUser,

  /// User has an active entitlement that is currently in a free trial period.
  /// Full app access during trial.
  trialUser,

  /// User has an active entitlement that has been paid for (not in trial).
  /// Full app access.
  proUser,

  /// User does not have an active entitlement but has had one in the past.
  /// "Immutable" read-only access - can view but not create/edit.
  freeUser,

  /// Initial or error state before the status is determined.
  unknown,
}
