import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/subscription/user_state_service.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Real-time user state that updates automatically when CustomerInfo changes
class UserStateNotifier extends StateNotifier<AsyncValue<UserState>> {
  final Ref _ref;
  UserState? _previousState;
  bool _isDisposed = false;
  StreamSubscription<CustomerInfo>? _customerInfoSubscription;

  UserStateNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initialize();
  }

  /// Constructor for non-signed-in users that immediately sets unknown state
  UserStateNotifier.forUnauthenticatedUser(this._ref)
      : super(const AsyncValue.data(UserState.unknown)) {
    // Don't initialize RevenueCat listeners for unauthenticated users
    Logger.debug('UserStateNotifier: Created for unauthenticated user');
    _previousState = UserState.unknown;
  }

  /// Initialize the notifier by setting up listeners and fetching initial state
  Future<void> _initialize() async {
    if (_isDisposed) return;

    Logger.debug('UserStateNotifier: Initializing');

    try {
      // Get the RevenueCat service
      final revenueCatService = _ref.read(revenueCatServiceProvider);

      // Wait for RevenueCat to be configured
      // This ensures we don't try to listen before the service is ready
      while (!revenueCatService.isConfigured && !_isDisposed) {
        Logger.debug(
          'UserStateNotifier: Waiting for RevenueCat to be configured...',
        );
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (_isDisposed) return;

      Logger.debug('UserStateNotifier: RevenueCat is configured, setting up listeners');

      // Listen to the centralized CustomerInfo stream from RevenueCatService
      _customerInfoSubscription = revenueCatService.customerInfoStream.listen(
        (customerInfo) {
          // Check if this notifier has been disposed to avoid "Bad state" errors
          if (_isDisposed) {
            Logger.debug(
              'UserStateNotifier: Ignoring CustomerInfo update - notifier is disposed',
            );
            return;
          }

          Logger.debug(
            'UserStateNotifier: Received CustomerInfo update from stream',
          );
          _updateUserStateFromCustomerInfo(customerInfo);
        },
        onError: (error) {
          if (_isDisposed) return;
          Logger.error('UserStateNotifier: Error in CustomerInfo stream', error);
          // Default to unknown state on error to be safe
          if (!_isDisposed) {
            state = const AsyncValue.data(UserState.unknown);
            _previousState = UserState.unknown;
          }
        },
      );

      // Get initial customer info
      await _fetchInitialUserState();
    } catch (e) {
      if (_isDisposed) return;
      Logger.error('UserStateNotifier: Error during initialization', e);
      if (!_isDisposed) {
        state = AsyncValue.error(e, StackTrace.current);
      }
    }
  }

  /// Fetch the initial user state from RevenueCat
  Future<void> _fetchInitialUserState() async {
    if (_isDisposed) return;

    Logger.debug('UserStateNotifier: Fetching initial user state');

    try {
      // Check if RevenueCat is configured before calling getCustomerInfo
      final revenueCatService = _ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug(
          'UserStateNotifier: RevenueCat not configured yet, defaulting to unknown state',
        );
        if (!_isDisposed) {
          state = const AsyncValue.data(UserState.unknown);
          _previousState = UserState.unknown;
        }
        return;
      }

      final customerInfo = await revenueCatService.getCustomerInfo();
      if (customerInfo != null) {
        _updateUserStateFromCustomerInfo(customerInfo);
      } else {
        // If customerInfo is null, default to unknown state
        if (!_isDisposed) {
          state = const AsyncValue.data(UserState.unknown);
          _previousState = UserState.unknown;
        }
      }
    } catch (e) {
      if (_isDisposed) return;
      Logger.error('UserStateNotifier: Error fetching initial user state', e);
      if (!_isDisposed) {
        state = AsyncValue.error(e, StackTrace.current);
      }
    }
  }

  /// Update user state from CustomerInfo using UserStateService
  void _updateUserStateFromCustomerInfo(CustomerInfo customerInfo) {
    if (_isDisposed) return;

    Logger.debug('UserStateNotifier: Updating user state from CustomerInfo');

    try {
      final userStateService = UserStateService.instance;
      final newUserState = userStateService.getUserState(customerInfo);

      Logger.debug('UserStateNotifier: User state determined as: $newUserState');

      // Only update if the state has actually changed
      if (_previousState != newUserState) {
        Logger.debug(
          'UserStateNotifier: User state changed from $_previousState to $newUserState',
        );
        
        if (!_isDisposed) {
          state = AsyncValue.data(newUserState);
          _previousState = newUserState;
        }
      } else {
        Logger.debug('UserStateNotifier: User state unchanged: $newUserState');
      }
    } catch (e) {
      if (_isDisposed) return;
      Logger.error('UserStateNotifier: Error updating user state', e);
      if (!_isDisposed) {
        state = AsyncValue.error(e, StackTrace.current);
      }
    }
  }

  /// Force refresh the user state
  /// Useful after purchases or other state changes
  Future<void> refresh() async {
    if (_isDisposed) return;
    
    Logger.debug('UserStateNotifier: Force refreshing user state');
    await _fetchInitialUserState();
  }

  @override
  void dispose() {
    Logger.debug('UserStateNotifier: Disposing');
    _isDisposed = true;
    _customerInfoSubscription?.cancel();
    super.dispose();
  }
}
