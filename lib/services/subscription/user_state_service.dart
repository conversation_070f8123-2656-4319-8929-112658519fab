import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/utils/logger.dart';

/// Service for determining the 4-state user monetization model
/// Based on RevenueCat CustomerInfo data
class UserStateService {
  static UserStateService? _instance;
  static UserStateService get instance => _instance ??= UserStateService._();

  UserStateService._();

  // Note: Using generic approach since specific product/entitlement IDs are not defined
  // The app currently checks for any active entitlements rather than specific ones

  /// Determine user state from RevenueCat CustomerInfo
  /// This is the core logic that implements the 4-state monetization model
  UserState getUserState(CustomerInfo customerInfo) {
    try {
      Logger.debug('UserStateService: Analyzing CustomerInfo for user state');

      // Check if user has any active entitlements
      final hasActiveEntitlements = customerInfo.entitlements.active.isNotEmpty;

      // 1. Check for active PRO or TRIAL status
      if (hasActiveEntitlements) {
        Logger.debug('UserStateService: User has active entitlements');

        // Check if any of the active entitlements is in trial period
        bool isInTrial = false;
        for (final entitlement in customerInfo.entitlements.active.values) {
          if (entitlement.periodType == PeriodType.trial) {
            isInTrial = true;
            break;
          }
        }

        if (isInTrial) {
          Logger.debug('UserStateService: User is in trial period');
          return UserState.trialUser;
        } else {
          // Covers normal, intro, etc. All are considered 'pro'.
          Logger.debug('UserStateService: User is pro (paid subscription)');
          return UserState.proUser;
        }
      }

      // 2. If no active entitlement, differentiate between NEW and FREE user
      else {
        Logger.debug('UserStateService: No active entitlement, checking purchase history');

        // Check if the user has EVER made any purchases
        // If they have any purchase history, they are a returning user (free)
        // If they have no purchase history, they are a new user
        if (customerInfo.allPurchaseDates.isNotEmpty) {
          Logger.debug('UserStateService: User has purchase history - free user');
          return UserState.freeUser;
        } else {
          Logger.debug('UserStateService: No purchase history - new user');
          return UserState.newUser;
        }
      }
    } catch (e) {
      Logger.error('UserStateService: Error determining user state', e);
      // In case of error, default to unknown state
      return UserState.unknown;
    }
  }

  /// Get user state asynchronously by fetching CustomerInfo
  /// Returns UserState.unknown if RevenueCat is not configured or on error
  Future<UserState> getUserStateAsync() async {
    try {
      // Check if RevenueCat is configured before calling getCustomerInfo
      if (!(await Purchases.isConfigured)) {
        Logger.debug(
          'UserStateService: Purchases SDK not configured yet. Returning unknown state.',
        );
        return UserState.unknown;
      }

      // Get customer info from RevenueCat
      final customerInfo = await Purchases.getCustomerInfo();
      return getUserState(customerInfo);
    } catch (e) {
      Logger.error('UserStateService: Failed to get user state async', e);
      // In case of error, return unknown state
      return UserState.unknown;
    }
  }

  /// Check if user has full app access (trial or pro users)
  bool hasFullAccess(UserState userState) {
    return userState == UserState.trialUser || userState == UserState.proUser;
  }

  /// Check if user should see paywall immediately (new users)
  bool shouldShowPaywall(UserState userState) {
    return userState == UserState.newUser;
  }

  /// Check if user has read-only access (free users)
  bool hasReadOnlyAccess(UserState userState) {
    return userState == UserState.freeUser;
  }

  /// Get user-friendly description of the user state
  String getUserStateDescription(UserState userState) {
    switch (userState) {
      case UserState.newUser:
        return 'New user - needs to start trial or subscribe';
      case UserState.trialUser:
        return 'Trial user - full access during trial period';
      case UserState.proUser:
        return 'Pro user - full paid access';
      case UserState.freeUser:
        return 'Free user - read-only access';
      case UserState.unknown:
        return 'Unknown user state';
    }
  }

  /// Legacy compatibility method - check if user is considered "pro"
  /// For backward compatibility with existing code
  bool isProUser(UserState userState) {
    return hasFullAccess(userState);
  }

  /// Legacy compatibility method - get user tier as string
  /// For backward compatibility with existing code
  String getUserTierString(UserState userState) {
    return hasFullAccess(userState) ? 'pro' : 'free';
  }
}
